import os
import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta

def fetch_and_save(tickers, start_date, end_date, save_path="data/market_data.csv"):
    os.makedirs(os.path.dirname(save_path), exist_ok=True)

    df_list = []
    for sym in tickers:
        data = yf.download(sym, start=start_date, end=end_date)
        data["Ticker"] = sym
        df_list.append(data.reset_index())
    full_df = pd.concat(df_list, ignore_index=True)
    full_df.sort_values(["Ticker", "Date"], inplace=True)

    # ticker-weise Target berechnen
    full_df["Target"] = 0
    for sym in tickers:
        mask = full_df["Ticker"] == sym
        closes = full_df.loc[mask, "Close"]
        full_df.loc[mask, "Target"] = (closes.shift(-1) > closes).astype(int)

    full_df.to_csv(save_path, index=False)
    print(f"✅ Market data saved to {save_path}")

if __name__ == "__main__":
    end = datetime.today().date()
    start = end - timedelta(days=365)
    fetch_and_save(
        tickers=["AAPL", "MSFT"],
        start_date=start.isoformat(),
        end_date=end.isoformat()
    )
