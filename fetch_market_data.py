import os
import pandas as pd
import yfinance as yf
from datetime import datetime, timed<PERSON>ta

def fetch_and_save(tickers, start_date, end_date, save_path="data/market_data.csv"):
    os.makedirs(os.path.dirname(save_path), exist_ok=True)

    df_list = []
    for sym in tickers:
        print(f"Fetching data for {sym}...")
        data = yf.download(sym, start=start_date, end=end_date)

        # Reset index to get Date as column
        data = data.reset_index()

        # Handle MultiIndex columns from yfinance
        if isinstance(data.columns, pd.MultiIndex):
            # Flatten multi-level columns: keep the first level (Price type)
            new_columns = []
            for col in data.columns:
                if col[0] == 'Date' or col[1] == '':
                    new_columns.append(col[0])  # Date column
                else:
                    new_columns.append(col[0])  # Price columns (Open, High, Low, Close, Volume)
            data.columns = new_columns

        # Add ticker column
        data["Ticker"] = sym

        # Remove duplicate columns if they exist
        data = data.loc[:, ~data.columns.duplicated()]

        # Ensure we have the expected columns
        required_cols = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            print(f"Warning: Missing columns for {sym}: {missing_cols}")
            continue

        # Select only the columns we need
        final_cols = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'Ticker']
        data = data[final_cols]

        # Ensure numeric types for price/volume columns
        numeric_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in numeric_cols:
            data[col] = pd.to_numeric(data[col], errors='coerce')

        # Remove rows with NaN values
        data = data.dropna(subset=numeric_cols)

        df_list.append(data)

    # Combine all data
    if not df_list:
        raise ValueError("No data was successfully fetched for any ticker!")

    full_df = pd.concat(df_list, ignore_index=True)
    full_df.sort_values(["Ticker", "Date"], inplace=True)

    # ticker-weise Target berechnen
    full_df["Target"] = 0.0
    for sym in tickers:
        mask = full_df["Ticker"] == sym
        if mask.sum() == 0:  # No data for this ticker
            continue
        closes = full_df.loc[mask, "Close"]
        # Calculate binary target: 1 if next day's close > today's close, 0 otherwise
        target_values = (closes.shift(-1) > closes).astype(float)
        full_df.loc[mask, "Target"] = target_values

    # Remove rows where Target is NaN (last row for each ticker)
    full_df = full_df.dropna(subset=['Target'])

    # Save with clean format
    full_df.to_csv(save_path, index=False)
    print(f"✅ Market data saved to {save_path}")
    print(f"📊 Data shape: {full_df.shape}")
    print(f"📈 Columns: {full_df.columns.tolist()}")
    print(f"🔢 Data types:\n{full_df.dtypes}")

if __name__ == "__main__":
    end = datetime.today().date()
    start = end - timedelta(days=730)  # 2 years of data instead of 1
    fetch_and_save(
        tickers=["AAPL", "MSFT"],
        start_date=start.isoformat(),
        end_date=end.isoformat()
    )
