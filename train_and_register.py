import os
import pandas as pd
import mlflow
import mlflow.pytorch
from pytorch_lightning import Trainer
from pytorch_lightning.callbacks import Model<PERSON>heckpoint, EarlyStopping, LearningRateMonitor
from models.ensemble.advanced_transformer import AdvancedFinancialTransformer
from data.fin_data_module import FinancialDataModule
import fetch_market_data

def main():
    # 1) Marktdaten abrufen und speichern
    fetch_market_data.fetch_and_save(
        tickers=["AAPL", "MSFT"],
        start_date="2024-01-01",
        end_date="2025-07-21"
    )

    # 2) CSV laden und bereinigen
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    data_path = os.path.join(BASE_DIR, "data", "market_data.csv")
    df_raw = pd.read_csv(data_path, parse_dates=["Date"])
    df_raw = df_raw.loc[:, ~df_raw.columns.duplicated()]

    # 3) Nur AAPL-Daten verwenden und index setzen
    df_aapl = df_raw[df_raw["Ticker"] == "AAPL"].copy()
    df_aapl.set_index("Date", inplace=True)
    if "Target" not in df_aapl.columns:
        raise ValueError("Spalte 'Target' fehlt in AAPL-Daten!")

    # 4) Feature-Matrix und Ziel definieren
    feature_cols = ["Open", "High", "Low", "Close", "Volume"]
    df_feats = df_aapl[feature_cols]
    targets = df_aapl["Target"]

    # 5) Hyperparameter definieren (vor DataModule!)
    params = {
        "input_features": df_feats.shape[1],
        "sequence_length": 120,
        "d_model": 512,
        "n_heads": 8,
        "n_encoder_layers": 6,
        "dropout": 0.1,
        "cnn_filters": 128,
        "lstm_units": 256,
        "output_size": 1,
        "learning_rate": 1e-4,
        "weight_decay": 1e-5,
        "use_mixed_precision": True,
        "uncertainty_estimation": True,
        "ensemble_size": 5
    }

    # … nach df_feats und targets definiert …
    print("Feature-Spalten:", df_feats.columns.tolist())
    print("Feature-Dtypes:\n", df_feats.dtypes)
    print("Feature-Shape:", df_feats.shape)
    print("Erste Zeilen:\n", df_feats.head())


    # 6) DataModule instanziieren
    dm = FinancialDataModule(
        features=df_feats,
        targets=targets,
        seq_len=params["sequence_length"],
        batch_size=128,
        val_split=0.2
    )

    # 7) MLflow Experiment konfigurieren
    mlflow.set_tracking_uri("http://localhost:5000")
    mlflow.set_experiment("V5Trader_Advanced_Models")

    # 8) Training und Registrierung
    with mlflow.start_run(run_name="advanced_transformer"):
        mlflow.log_params(params)
        model = AdvancedFinancialTransformer(**params)

        ckpt = ModelCheckpoint(monitor="val_loss", save_top_k=1, mode="min", filename="best_model")
        es = EarlyStopping(monitor="val_loss", patience=10, mode="min")
        lr_monitor = LearningRateMonitor(logging_interval="step")

        trainer = Trainer(
            max_epochs=100,
            accelerator="gpu" if os.getenv("CUDA_VISIBLE_DEVICES") else "cpu",
            devices=1,
            precision=16,
            callbacks=[ckpt, es, lr_monitor],
            log_every_n_steps=10
        )
        trainer.fit(model, dm)

        mlflow.pytorch.log_model(model, artifact_path="model")
        run_id = mlflow.active_run().info.run_id
        model_uri = f"runs:/{run_id}/model"
        mlflow.register_model(model_uri, "V5Trader_AdvancedTransformer")

if __name__ == "__main__":
    main()
