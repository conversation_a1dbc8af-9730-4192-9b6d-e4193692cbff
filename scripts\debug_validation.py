import pandas as pd
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.fin_data_module import TimeSeriesDataset

# Test no numeric features
non_numeric_features = pd.DataFrame({
    'text_col': ['a', 'b', 'c'],
    'another_text': ['x', 'y', 'z']
})

print("Original data:")
print(non_numeric_features)
print("Dtypes:", non_numeric_features.dtypes)

# Test what happens in TimeSeriesDataset
features_clean = non_numeric_features.copy()
for col in features_clean.columns:
    features_clean[col] = pd.to_numeric(features_clean[col], errors='coerce')

print("\nAfter to_numeric conversion:")
print(features_clean)
print("Dtypes:", features_clean.dtypes)

features_num = features_clean.select_dtypes(include=["number"]).ffill().fillna(0.0)
print("\nAfter select_dtypes and fillna:")
print(features_num)
print("Shape:", features_num.shape)
print("Empty?", features_num.empty)

if features_num.empty or features_num.shape[1] == 0:
    print("Would raise ValueError!")
else:
    print("Would NOT raise ValueError")

# Test with actual TimeSeriesDataset
print("\n" + "="*50)
print("Testing with TimeSeriesDataset:")
try:
    dataset = TimeSeriesDataset(non_numeric_features, pd.Series([1, 2, 3]), 2)
    print("❌ No error raised!")
except ValueError as e:
    print(f"✅ Error raised: {e}")
