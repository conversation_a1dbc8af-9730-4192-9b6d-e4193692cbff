-- V5_Trader PostgreSQL Initialization Script
-- Creates database schema for trading system with MLflow integration

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create schemas
CREATE SCHEMA IF NOT EXISTS trading;
CREATE SCHEMA IF NOT EXISTS mlflow;
CREATE SCHEMA IF NOT EXISTS monitoring;

-- Market data table
CREATE TABLE IF NOT EXISTS trading.market_data (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    date DATE NOT NULL,
    open DECIMAL(10,4) NOT NULL,
    high DECIMAL(10,4) NOT NULL,
    low DECIMAL(10,4) NOT NULL,
    close DECIMAL(10,4) NOT NULL,
    volume BIGINT NOT NULL,
    adjusted_close DECIMAL(10,4),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(symbol, date)
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_market_data_symbol_date ON trading.market_data(symbol, date DESC);
CREATE INDEX IF NOT EXISTS idx_market_data_date ON trading.market_data(date DESC);

-- Trading signals table
CREATE TABLE IF NOT EXISTS trading.signals (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    signal_type VARCHAR(20) NOT NULL, -- 'BUY', 'SELL', 'HOLD'
    confidence DECIMAL(5,4) NOT NULL,
    price DECIMAL(10,4) NOT NULL,
    model_version VARCHAR(50),
    features JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_signals_symbol_date ON trading.signals(symbol, created_at DESC);

-- Portfolio positions table
CREATE TABLE IF NOT EXISTS trading.positions (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    quantity INTEGER NOT NULL,
    entry_price DECIMAL(10,4) NOT NULL,
    current_price DECIMAL(10,4),
    pnl DECIMAL(12,4),
    status VARCHAR(20) DEFAULT 'OPEN', -- 'OPEN', 'CLOSED'
    opened_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    closed_at TIMESTAMP
);

-- Trading transactions table
CREATE TABLE IF NOT EXISTS trading.transactions (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    transaction_type VARCHAR(10) NOT NULL, -- 'BUY', 'SELL'
    quantity INTEGER NOT NULL,
    price DECIMAL(10,4) NOT NULL,
    commission DECIMAL(8,4) DEFAULT 0,
    total_amount DECIMAL(12,4) NOT NULL,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Model performance tracking
CREATE TABLE IF NOT EXISTS mlflow.model_performance (
    id SERIAL PRIMARY KEY,
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50) NOT NULL,
    run_id VARCHAR(100),
    metric_name VARCHAR(50) NOT NULL,
    metric_value DECIMAL(10,6) NOT NULL,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- System metrics table
CREATE TABLE IF NOT EXISTS monitoring.system_metrics (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,6) NOT NULL,
    labels JSONB,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_system_metrics_name_time ON monitoring.system_metrics(metric_name, recorded_at DESC);

-- API request logs
CREATE TABLE IF NOT EXISTS monitoring.api_requests (
    id SERIAL PRIMARY KEY,
    method VARCHAR(10) NOT NULL,
    endpoint VARCHAR(200) NOT NULL,
    status_code INTEGER NOT NULL,
    response_time_ms INTEGER NOT NULL,
    user_agent TEXT,
    ip_address INET,
    request_id UUID DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_api_requests_endpoint_time ON monitoring.api_requests(endpoint, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_api_requests_status_time ON monitoring.api_requests(status_code, created_at DESC);

-- Create views for common queries
CREATE OR REPLACE VIEW trading.latest_prices AS
SELECT DISTINCT ON (symbol) 
    symbol, 
    close as price, 
    date,
    volume
FROM trading.market_data 
ORDER BY symbol, date DESC;

CREATE OR REPLACE VIEW trading.portfolio_summary AS
SELECT 
    symbol,
    SUM(CASE WHEN status = 'OPEN' THEN quantity ELSE 0 END) as open_quantity,
    AVG(CASE WHEN status = 'OPEN' THEN entry_price ELSE NULL END) as avg_entry_price,
    SUM(CASE WHEN status = 'OPEN' THEN pnl ELSE 0 END) as unrealized_pnl,
    SUM(CASE WHEN status = 'CLOSED' THEN pnl ELSE 0 END) as realized_pnl
FROM trading.positions 
GROUP BY symbol;

-- Create functions for common operations
CREATE OR REPLACE FUNCTION trading.calculate_portfolio_value()
RETURNS DECIMAL(15,4) AS $$
DECLARE
    total_value DECIMAL(15,4) := 0;
BEGIN
    SELECT COALESCE(SUM(p.quantity * lp.price), 0)
    INTO total_value
    FROM trading.positions p
    JOIN trading.latest_prices lp ON p.symbol = lp.symbol
    WHERE p.status = 'OPEN';
    
    RETURN total_value;
END;
$$ LANGUAGE plpgsql;

-- Insert sample data for testing
INSERT INTO trading.market_data (symbol, date, open, high, low, close, volume) VALUES
('AAPL', '2024-01-01', 150.00, 155.00, 149.00, 154.00, 1000000),
('MSFT', '2024-01-01', 300.00, 305.00, 298.00, 303.00, 800000),
('GOOGL', '2024-01-01', 2800.00, 2850.00, 2780.00, 2820.00, 500000)
ON CONFLICT (symbol, date) DO NOTHING;

-- Create user for API Gateway
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'v5trader_api') THEN
        CREATE ROLE v5trader_api WITH LOGIN PASSWORD 'api_secure_password_2024';
    END IF;
END
$$;

-- Grant permissions
GRANT USAGE ON SCHEMA trading TO v5trader_api;
GRANT USAGE ON SCHEMA mlflow TO v5trader_api;
GRANT USAGE ON SCHEMA monitoring TO v5trader_api;

GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA trading TO v5trader_api;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA mlflow TO v5trader_api;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA monitoring TO v5trader_api;

GRANT USAGE ON ALL SEQUENCES IN SCHEMA trading TO v5trader_api;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA mlflow TO v5trader_api;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA monitoring TO v5trader_api;

-- Create materialized view for performance
CREATE MATERIALIZED VIEW IF NOT EXISTS trading.daily_returns AS
SELECT 
    symbol,
    date,
    close,
    LAG(close) OVER (PARTITION BY symbol ORDER BY date) as prev_close,
    (close - LAG(close) OVER (PARTITION BY symbol ORDER BY date)) / LAG(close) OVER (PARTITION BY symbol ORDER BY date) * 100 as daily_return
FROM trading.market_data
ORDER BY symbol, date;

CREATE UNIQUE INDEX IF NOT EXISTS idx_daily_returns_symbol_date ON trading.daily_returns(symbol, date);

-- Refresh materialized view function
CREATE OR REPLACE FUNCTION trading.refresh_daily_returns()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY trading.daily_returns;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update portfolio PnL
CREATE OR REPLACE FUNCTION trading.update_position_pnl()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE trading.positions 
    SET 
        current_price = NEW.close,
        pnl = (NEW.close - entry_price) * quantity
    WHERE symbol = NEW.symbol AND status = 'OPEN';
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_pnl
    AFTER INSERT OR UPDATE ON trading.market_data
    FOR EACH ROW
    EXECUTE FUNCTION trading.update_position_pnl();

-- Vacuum and analyze for performance
VACUUM ANALYZE;
