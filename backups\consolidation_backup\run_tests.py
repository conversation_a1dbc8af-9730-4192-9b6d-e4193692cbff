#!/usr/bin/env python3
"""
Test runner for V5_Trader project
Runs all test suites and provides comprehensive reporting
"""
import os
import sys
import time
import traceback
from datetime import datetime


def run_test_module(module_path, module_name):
    """Run a specific test module and return results"""
    print(f"\n{'='*60}")
    print(f"🧪 Running {module_name}")
    print(f"{'='*60}")
    
    start_time = time.time()
    success = False
    error_msg = None
    
    try:
        # Import and run the test module
        sys.path.insert(0, os.path.dirname(module_path))
        
        if module_name == "Import Tests":
            import test_imports
            print("✅ All imports successful!")
            success = True
            
        elif module_name == "Data Module Tests":
            import test_dm
            print("✅ Data module tests completed!")
            success = True
            
        elif module_name == "Data Pipeline Tests":
            from tests.test_data_pipeline import TestDataPipeline
            test_suite = TestDataPipeline()
            test_suite.setup_method()
            
            test_suite.test_timeseries_dataset_creation()
            test_suite.test_timeseries_dataset_numeric_conversion()
            test_suite.test_financial_data_module()
            test_suite.test_fetch_market_data_structure()
            test_suite.test_target_calculation()
            test_suite.test_data_validation()
            
            print("✅ All data pipeline tests passed!")
            success = True
            
        elif module_name == "Model Integration Tests":
            from tests.test_model_integration import TestModelIntegration
            test_suite = TestModelIntegration()
            test_suite.setup_method()
            
            test_suite.test_model_initialization()
            test_suite.test_model_forward_pass()
            test_suite.test_model_with_uncertainty()
            test_suite.test_model_with_attention()
            test_suite.test_data_module_model_integration()
            test_suite.test_training_step()
            test_suite.test_validation_step()
            test_suite.test_optimizer_configuration()
            test_suite.test_model_device_compatibility()
            
            print("✅ All model integration tests passed!")
            success = True
            
        elif module_name == "End-to-End Tests":
            from tests.test_end_to_end import TestEndToEnd
            test_suite = TestEndToEnd()
            test_suite.setup_method()
            
            try:
                test_suite.test_data_quality_checks()
                test_suite.test_complete_pipeline()
                print("✅ All end-to-end tests passed!")
                success = True
            finally:
                test_suite.teardown_method()
                
    except Exception as e:
        error_msg = str(e)
        print(f"❌ {module_name} failed: {error_msg}")
        traceback.print_exc()
        
    end_time = time.time()
    duration = end_time - start_time
    
    return {
        'name': module_name,
        'success': success,
        'duration': duration,
        'error': error_msg
    }


def main():
    """Main test runner"""
    print("🚀 V5_Trader Test Suite")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python version: {sys.version}")
    print(f"Working directory: {os.getcwd()}")
    
    # Define test modules to run
    test_modules = [
        ("test_imports.py", "Import Tests"),
        ("test_dm.py", "Data Module Tests"),
        ("tests/test_data_pipeline.py", "Data Pipeline Tests"),
        ("tests/test_model_integration.py", "Model Integration Tests"),
        ("tests/test_end_to_end.py", "End-to-End Tests"),
    ]
    
    # Run all tests
    results = []
    total_start_time = time.time()
    
    for module_path, module_name in test_modules:
        if os.path.exists(module_path):
            result = run_test_module(module_path, module_name)
            results.append(result)
        else:
            print(f"⚠️  Test module not found: {module_path}")
            results.append({
                'name': module_name,
                'success': False,
                'duration': 0,
                'error': f"Module not found: {module_path}"
            })
    
    total_duration = time.time() - total_start_time
    
    # Print summary
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = sum(1 for r in results if r['success'])
    failed = len(results) - passed
    
    print(f"Total tests run: {len(results)}")
    print(f"Passed: {passed} ✅")
    print(f"Failed: {failed} ❌")
    print(f"Total duration: {total_duration:.2f}s")
    
    print(f"\n{'Test Name':<25} {'Status':<10} {'Duration':<10} {'Error'}")
    print("-" * 70)
    
    for result in results:
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        duration = f"{result['duration']:.2f}s"
        error = result['error'][:30] + "..." if result['error'] and len(result['error']) > 30 else (result['error'] or "")
        print(f"{result['name']:<25} {status:<10} {duration:<10} {error}")
    
    if failed > 0:
        print(f"\n❌ {failed} test(s) failed. Please check the errors above.")
        return 1
    else:
        print(f"\n🎉 All {passed} tests passed successfully!")
        return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
