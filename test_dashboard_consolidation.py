#!/usr/bin/env python3
"""
Test script to verify dashboard consolidation
Ensures CLI dashboard is removed and web dashboard works via API Gateway
"""
import requests
import subprocess
import sys
from pathlib import Path
import time

def test_cli_dashboard_removed():
    """Test that CLI dashboard files are removed"""
    print("🧪 Testing CLI dashboard removal...")
    
    cli_dashboard_files = [
        "tools/dashboard.py",
        "test_dashboard.py"
    ]
    
    missing_files = []
    for file_path in cli_dashboard_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if len(missing_files) == len(cli_dashboard_files):
        print("✅ CLI dashboard files successfully removed")
        return True
    else:
        existing_files = [f for f in cli_dashboard_files if Path(f).exists()]
        print(f"❌ CLI dashboard files still exist: {existing_files}")
        return False

def test_api_gateway_static_mount():
    """Test that API Gateway serves static files"""
    print("🧪 Testing API Gateway static file serving...")
    
    try:
        # Test dashboard access via API Gateway
        response = requests.get("http://localhost:8000/dashboard/dashboard.html", timeout=10)
        
        if response.status_code == 200:
            if "V5 Trader Dashboard" in response.text:
                print("✅ Web dashboard accessible via API Gateway")
                return True
            else:
                print("❌ Dashboard content not found")
                return False
        else:
            print(f"❌ API Gateway returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API Gateway static files test failed: {e}")
        return False

def test_prometheus_metrics():
    """Test that Prometheus metrics are available"""
    print("🧪 Testing Prometheus metrics endpoint...")
    
    try:
        response = requests.get("http://localhost:8000/metrics", timeout=10)
        
        if response.status_code == 200:
            metrics_text = response.text
            
            # Check for key metrics
            expected_metrics = [
                "http_requests_total",
                "http_request_duration_seconds"
            ]
            
            found_metrics = []
            for metric in expected_metrics:
                if metric in metrics_text:
                    found_metrics.append(metric)
            
            if len(found_metrics) == len(expected_metrics):
                print("✅ Prometheus metrics endpoint working")
                return True
            else:
                missing = [m for m in expected_metrics if m not in found_metrics]
                print(f"❌ Missing metrics: {missing}")
                return False
        else:
            print(f"❌ Metrics endpoint returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Prometheus metrics test failed: {e}")
        return False

def test_health_endpoint_enhanced():
    """Test that health endpoint includes system resources"""
    print("🧪 Testing enhanced health endpoint...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        
        if response.status_code == 200:
            health_data = response.json()
            
            required_fields = ["status", "timestamp", "services"]
            missing_fields = [field for field in required_fields if field not in health_data]
            
            if missing_fields:
                print(f"❌ Missing health fields: {missing_fields}")
                return False
            
            # Check if system resources are available
            if "system_resources" in health_data:
                print("✅ Health endpoint includes system resources")
                resources = health_data["system_resources"]
                resource_fields = ["cpu_percent", "memory_percent", "disk_percent"]
                
                available_resources = [field for field in resource_fields if field in resources]
                print(f"📊 Available resources: {available_resources}")
                
            print("✅ Enhanced health endpoint working")
            return True
        else:
            print(f"❌ Health endpoint returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Health endpoint test failed: {e}")
        return False

def test_no_cli_references():
    """Test that no CLI dashboard references remain"""
    print("🧪 Testing for remaining CLI dashboard references...")
    
    try:
        # Search for dashboard.py references in Python files
        result = subprocess.run(
            ['findstr', '/s', '/i', 'dashboard.py', '*.py'],
            capture_output=True,
            text=True,
            cwd=Path.cwd()
        )
        
        if result.returncode != 0:
            print("✅ No CLI dashboard references found in Python files")
            return True
        else:
            print("❌ Found CLI dashboard references:")
            print(result.stdout)
            return False
            
    except Exception as e:
        print(f"⚠️ Reference check failed (may be expected): {e}")
        return True  # Don't fail the test if findstr is not available

def test_web_dashboard_functionality():
    """Test that web dashboard has all required elements"""
    print("🧪 Testing web dashboard functionality...")
    
    dashboard_path = Path("static/dashboard.html")
    
    if not dashboard_path.exists():
        print("❌ Web dashboard file not found")
        return False
    
    try:
        with open(dashboard_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_elements = [
            "System Status",
            "System Resources",
            "Live Kurse",
            "Portfolio",
            "Trading Signale",
            "WebSocket",
            "cpu-usage",
            "memory-usage",
            "disk-usage",
            "prometheus-status",
            "grafana-status"
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ Missing dashboard elements: {missing_elements}")
            return False
        
        print("✅ Web dashboard has all required elements")
        return True
        
    except Exception as e:
        print(f"❌ Error reading dashboard file: {e}")
        return False

def main():
    """Run all consolidation tests"""
    print("🚀 DASHBOARD CONSOLIDATION VERIFICATION")
    print("=" * 60)
    
    tests = [
        ("CLI Dashboard Removed", test_cli_dashboard_removed),
        ("API Gateway Static Mount", test_api_gateway_static_mount),
        ("Prometheus Metrics", test_prometheus_metrics),
        ("Enhanced Health Endpoint", test_health_endpoint_enhanced),
        ("No CLI References", test_no_cli_references),
        ("Web Dashboard Functionality", test_web_dashboard_functionality),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DASHBOARD CONSOLIDATION TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<30}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 DASHBOARD CONSOLIDATION SUCCESSFUL!")
        print("🌐 Web dashboard is now the single source of truth!")
        
        print("\n📋 Access Points:")
        print("  Web Dashboard:  http://localhost:8000/dashboard/dashboard.html")
        print("  Health API:     http://localhost:8000/health")
        print("  Metrics API:    http://localhost:8000/metrics")
        print("  Prometheus:     http://localhost:9090")
        print("  Grafana:        http://localhost:3000")
        
        return True
    else:
        print(f"\n❌ DASHBOARD CONSOLIDATION INCOMPLETE!")
        print(f"Please fix the {total - passed} failing test(s).")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
