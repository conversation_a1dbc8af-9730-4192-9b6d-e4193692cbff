#!/usr/bin/env python3
"""
Consolidated integration tests for V5_Trader system
Combines all integration testing into a single, comprehensive suite
"""
import pytest
import asyncio
import logging
import sys
from pathlib import Path
from datetime import datetime
import requests
import websockets
import json
import subprocess

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config.config_loader import get_config_loader
from data.fin_data_module import FinancialDataModule
from models.ensemble.advanced_transformer import AdvancedFinancialTransformer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestSystemIntegration:
    """Comprehensive system integration tests"""
    
    @pytest.fixture(scope="class")
    def config_loader(self):
        """Load configuration for tests"""
        return get_config_loader()
    
    @pytest.fixture(scope="class")
    def data_module(self):
        """Create data module for tests"""
        return FinancialDataModule(
            data_path="data/market_data.csv",
            sequence_length=60,
            batch_size=16,
            val_split=0.1,
            test_split=0.1
        )
    
    def test_configuration_loading(self, config_loader):
        """Test configuration system"""
        logger.info("🧪 Testing configuration loading...")
        
        # Test configuration validation
        assert config_loader.validate_config(), "Configuration validation failed"
        
        # Test configuration access
        model_config = config_loader.get_model_config()
        assert model_config.name == "V5Trader_AdvancedTransformer"
        assert model_config.input_features > 0
        assert model_config.sequence_length > 0
        
        mlflow_config = config_loader.get_mlflow_config()
        assert mlflow_config.tracking_uri is not None
        assert mlflow_config.experiment_name is not None
        
        logger.info("✅ Configuration loading test passed")
    
    def test_data_pipeline(self, data_module):
        """Test data pipeline functionality"""
        logger.info("🧪 Testing data pipeline...")
        
        # Setup data module
        data_module.setup()
        
        # Test data loaders
        train_loader = data_module.train_dataloader()
        val_loader = data_module.val_dataloader()
        test_loader = data_module.test_dataloader()
        
        assert len(train_loader) > 0, "Train loader is empty"
        assert len(val_loader) > 0, "Validation loader is empty"
        assert len(test_loader) > 0, "Test loader is empty"
        
        # Test batch shapes
        train_batch = next(iter(train_loader))
        x, y = train_batch
        
        assert len(x.shape) == 3, f"Expected 3D input, got {x.shape}"
        assert len(y.shape) == 1, f"Expected 1D target, got {y.shape}"
        assert x.shape[0] == y.shape[0], "Batch size mismatch"
        
        logger.info(f"✅ Data pipeline test passed - batch shape: {x.shape}")
    
    def test_model_creation(self, data_module):
        """Test model creation and forward pass"""
        logger.info("🧪 Testing model creation...")
        
        # Setup data
        data_module.setup()
        sample_batch = next(iter(data_module.train_dataloader()))
        x, y = sample_batch
        input_features = x.shape[-1]
        
        # Create model
        model = AdvancedFinancialTransformer(
            input_features=input_features,
            sequence_length=60,
            d_model=128,
            n_heads=4,
            n_encoder_layers=2,
            dropout=0.1,
            learning_rate=1e-4
        )
        
        # Test forward pass
        model.eval()
        with torch.no_grad():
            output = model(x)
            assert output.shape[0] == x.shape[0], "Batch size mismatch in output"
            assert len(output.shape) == 2, f"Expected 2D output, got {output.shape}"
        
        # Test parameter count
        total_params = sum(p.numel() for p in model.parameters())
        assert total_params > 0, "Model has no parameters"
        
        logger.info(f"✅ Model creation test passed - {total_params:,} parameters")
    
    def test_websocket_server(self):
        """Test WebSocket server functionality"""
        logger.info("🧪 Testing WebSocket server...")
        
        async def websocket_test():
            try:
                # Try to connect to WebSocket server
                uri = "ws://localhost:8765"
                async with websockets.connect(uri, timeout=5) as websocket:
                    # Send subscription message
                    subscribe_msg = {
                        "type": "subscribe",
                        "symbols": ["AAPL", "MSFT"]
                    }
                    await websocket.send(json.dumps(subscribe_msg))
                    
                    # Wait for response
                    response = await asyncio.wait_for(websocket.recv(), timeout=5)
                    data = json.loads(response)
                    
                    assert "type" in data, "Response missing type field"
                    return True
                    
            except Exception as e:
                logger.warning(f"WebSocket test failed: {e}")
                return False
        
        # Run async test
        try:
            result = asyncio.run(websocket_test())
            if result:
                logger.info("✅ WebSocket server test passed")
            else:
                logger.warning("⚠️ WebSocket server not available")
        except Exception as e:
            logger.warning(f"⚠️ WebSocket test skipped: {e}")
    
    def test_api_gateway(self):
        """Test API Gateway functionality"""
        logger.info("🧪 Testing API Gateway...")
        
        try:
            # Test health endpoint
            response = requests.get("http://localhost:8000/health", timeout=5)
            
            if response.status_code == 200:
                health_data = response.json()
                assert "status" in health_data, "Health response missing status"
                assert "timestamp" in health_data, "Health response missing timestamp"
                
                # Test metrics endpoint
                metrics_response = requests.get("http://localhost:8000/metrics", timeout=5)
                assert metrics_response.status_code == 200, "Metrics endpoint failed"
                
                logger.info("✅ API Gateway test passed")
            else:
                logger.warning("⚠️ API Gateway not available")
                
        except Exception as e:
            logger.warning(f"⚠️ API Gateway test skipped: {e}")
    
    def test_monitoring_stack(self):
        """Test monitoring stack (Prometheus, Grafana)"""
        logger.info("🧪 Testing monitoring stack...")
        
        services = {
            "Prometheus": "http://localhost:9090/api/v1/targets",
            "Grafana": "http://localhost:3000/api/health",
            "MLflow": "http://localhost:5000/api/2.0/mlflow/experiments/list"
        }
        
        results = {}
        for service_name, url in services.items():
            try:
                response = requests.get(url, timeout=5)
                results[service_name] = response.status_code == 200
                
                if results[service_name]:
                    logger.info(f"✅ {service_name} is available")
                else:
                    logger.warning(f"⚠️ {service_name} returned status {response.status_code}")
                    
            except Exception as e:
                results[service_name] = False
                logger.warning(f"⚠️ {service_name} not available: {e}")
        
        # At least one monitoring service should be available
        available_services = sum(results.values())
        logger.info(f"📊 Monitoring stack: {available_services}/{len(services)} services available")
    
    def test_docker_services(self):
        """Test Docker services status"""
        logger.info("🧪 Testing Docker services...")
        
        try:
            # Check docker-compose status
            result = subprocess.run(
                ['docker-compose', 'ps'], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if result.returncode == 0:
                running_services = []
                for line in result.stdout.split('\n'):
                    if 'Up' in line:
                        service_name = line.split()[0]
                        running_services.append(service_name)
                
                logger.info(f"✅ Docker services running: {', '.join(running_services)}")
            else:
                logger.warning("⚠️ Docker Compose not running")
                
        except Exception as e:
            logger.warning(f"⚠️ Docker services test skipped: {e}")
    
    def test_file_structure(self):
        """Test project file structure"""
        logger.info("🧪 Testing file structure...")
        
        required_files = [
            "config/model_config.yaml",
            "config/config_loader.py",
            "data/market_data.csv",
            "models/ensemble/advanced_transformer.py",
            "models/components/__init__.py",
            "services/websocket_server.py",
            "docker-compose.yml",
            "requirements.txt"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            logger.warning(f"⚠️ Missing files: {missing_files}")
        else:
            logger.info("✅ All required files present")
        
        assert len(missing_files) == 0, f"Missing required files: {missing_files}"

class TestSystemPerformance:
    """Performance and load tests"""
    
    def test_model_inference_speed(self):
        """Test model inference performance"""
        logger.info("🧪 Testing model inference speed...")
        
        import torch
        import time
        
        # Create sample data
        batch_size = 32
        sequence_length = 60
        input_features = 5
        
        x = torch.randn(batch_size, sequence_length, input_features)
        
        # Create model
        model = AdvancedFinancialTransformer(
            input_features=input_features,
            sequence_length=sequence_length,
            d_model=128,
            n_heads=4,
            n_encoder_layers=2
        )
        model.eval()
        
        # Warm up
        with torch.no_grad():
            for _ in range(5):
                _ = model(x)
        
        # Measure inference time
        start_time = time.time()
        num_inferences = 100
        
        with torch.no_grad():
            for _ in range(num_inferences):
                _ = model(x)
        
        end_time = time.time()
        avg_inference_time = (end_time - start_time) / num_inferences * 1000  # ms
        
        logger.info(f"✅ Average inference time: {avg_inference_time:.2f}ms")
        
        # Assert reasonable performance (should be < 100ms for this model size)
        assert avg_inference_time < 100, f"Inference too slow: {avg_inference_time:.2f}ms"

def run_integration_tests():
    """Run all integration tests"""
    logger.info("🚀 V5_TRADER INTEGRATION TESTS")
    logger.info("=" * 60)
    logger.info(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 60)
    
    # Run pytest
    pytest_args = [
        __file__,
        "-v",
        "--tb=short",
        "--disable-warnings"
    ]
    
    exit_code = pytest.main(pytest_args)
    
    logger.info("=" * 60)
    if exit_code == 0:
        logger.info("🎉 ALL INTEGRATION TESTS PASSED!")
    else:
        logger.error("❌ SOME INTEGRATION TESTS FAILED!")
    logger.info("=" * 60)
    
    return exit_code == 0

if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
