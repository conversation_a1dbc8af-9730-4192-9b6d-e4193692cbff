import os
import time
from datetime import datetime
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.cuda.amp import autocast, GradScaler
import pytorch_lightning as pl
from torchmetrics import MeanSquaredError, MeanAbsoluteError
import mlflow
import mlflow.pytorch


class AdvancedFeatureEngineering:
    """
    Hochmoderne Feature Engineering Pipeline für Finanzdaten
    Implementiert State-of-the-Art Techniken aus der Quantitative Finance
    """
    
    def __init__(self):
        self.feature_generators = {
            'technical': self._technical_indicators,
            'microstructure': self._market_microstructure, 
            'alternative': self._alternative_data_features,
            'macro': self._macroeconomic_features,
            'cross_asset': self._cross_asset_features,
            'temporal': self._temporal_features
        }
    
    def _technical_indicators(self, df):
        """Advanced Technical Indicators"""
        features = pd.DataFrame(index=df.index)
        
        # Multi-timeframe Moving Averages
        for period in [5, 10, 20, 50, 100, 200]:
            features[f'sma_{period}'] = df['close'].rolling(period).mean()
            features[f'ema_{period}'] = df['close'].ewm(span=period).mean()
            
        # Bollinger Bands with multiple standard deviations
        for std_dev in [1.5, 2.0, 2.5]:
            rolling_mean = df['close'].rolling(20).mean()
            rolling_std = df['close'].rolling(20).std()
            features[f'bb_upper_{std_dev}'] = rolling_mean + (rolling_std * std_dev)
            features[f'bb_lower_{std_dev}'] = rolling_mean - (rolling_std * std_dev)
            features[f'bb_width_{std_dev}'] = features[f'bb_upper_{std_dev}'] - features[f'bb_lower_{std_dev}']
        
        # Advanced Momentum Indicators
        features['rsi'] = self._calculate_rsi(df['close'])
        features['stoch_k'], features['stoch_d'] = self._calculate_stochastic(df)
        features['williams_r'] = self._calculate_williams_r(df)
        
        # MACD Family
        features['macd'], features['macd_signal'], features['macd_histogram'] = self._calculate_macd(df['close'])
        
        # Volatility Indicators
        features['atr'] = self._calculate_atr(df)
        features['garch_volatility'] = self._calculate_garch_volatility(df['close'])
        
        return features
    
    def _market_microstructure(self, df):
        """Market Microstructure Features"""
        features = pd.DataFrame(index=df.index)
        
        # Order Flow Imbalance
        features['bid_ask_spread'] = (df['ask'] - df['bid']) / df['close']
        features['order_flow_imbalance'] = (df['buy_volume'] - df['sell_volume']) / df['volume']
        
        # Volume Profile
        features['volume_weighted_price'] = (df['volume'] * df['close']).cumsum() / df['volume'].cumsum()
        features['relative_volume'] = df['volume'] / df['volume'].rolling(20).mean()
        
        # Price Impact Measures
        features['price_impact'] = df['close'].pct_change() / np.log(df['volume'] + 1)
        
        return features
    
    def _alternative_data_features(self, df, news_sentiment=None, social_sentiment=None):
        """Alternative Data Integration"""
        features = pd.DataFrame(index=df.index)
        
        if news_sentiment is not None:
            features['news_sentiment'] = news_sentiment
            features['news_sentiment_ma'] = news_sentiment.rolling(5).mean()
        
        if social_sentiment is not None:
            features['social_sentiment'] = social_sentiment
            features['social_buzz'] = social_sentiment.rolling(3).std()
            
        return features
    
    def _cross_asset_features(self, df, market_indices=None):
        """Cross-Asset Features"""
        features = pd.DataFrame(index=df.index)
        
        if market_indices is not None:
            for name, index_data in market_indices.items():
                # Beta calculation
                returns = df['close'].pct_change()
                index_returns = index_data.pct_change()
                
                rolling_beta = returns.rolling(60).corr(index_returns)
                features[f'beta_{name}'] = rolling_beta
                
                # Relative Strength
                features[f'relative_strength_{name}'] = (returns.rolling(20).mean() / 
                                                       index_returns.rolling(20).mean())
        
        return features
    
    def generate_features(self, df, **kwargs):
        """Hauptmethode für Feature-Generierung"""
        all_features = [df[['open', 'high', 'low', 'close', 'volume']]]
        
        for feature_type, generator in self.feature_generators.items():
            try:
                if feature_type in ['alternative', 'cross_asset']:
                    features = generator(df, **kwargs)
                else:
                    features = generator(df)
                all_features.append(features)
            except Exception as e:
                print(f"Warnung: Fehler bei {feature_type} Features: {e}")
                
        combined_features = pd.concat(all_features, axis=1)
        
        # Feature Scaling und Normalization
        from sklearn.preprocessing import RobustScaler
        scaler = RobustScaler()
        scaled_features = scaler.fit_transform(combined_features.fillna(0))
        
        return pd.DataFrame(scaled_features, 
                          index=combined_features.index,
                          columns=combined_features.columns), scaler
