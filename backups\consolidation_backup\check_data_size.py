import pandas as pd
import os

# Load data
df_raw = pd.read_csv(os.path.join("data", "market_data.csv"), parse_dates=["Date"])
print("Total data shape:", df_raw.shape)

# Check each ticker
for ticker in df_raw["Ticker"].unique():
    ticker_data = df_raw[df_raw["Ticker"] == ticker]
    print(f"\n{ticker}:")
    print(f"  Total rows: {len(ticker_data)}")
    
    # Calculate available windows for different sequence lengths
    for seq_len in [60, 90, 120, 150]:
        windows = len(ticker_data) - seq_len
        print(f"  seq_len={seq_len}: {windows} windows available")
        
        # Calculate train/val split
        val_split = 0.2
        val_windows = int(windows * val_split)
        train_windows = windows - val_windows
        print(f"    Train: {train_windows}, Val: {val_windows}")

# Check date range
print(f"\nDate range: {df_raw['Date'].min()} to {df_raw['Date'].max()}")
print(f"Total days: {(df_raw['Date'].max() - df_raw['Date'].min()).days}")

# Suggest optimal sequence length
aapl_data = df_raw[df_raw["Ticker"] == "AAPL"]
data_length = len(aapl_data)
print(f"\nOptimal sequence length suggestions for {data_length} data points:")
for min_val_batches in [5, 10, 20]:
    # We want at least min_val_batches validation windows
    # With 20% validation split: val_windows = 0.2 * (data_length - seq_len)
    # So: min_val_batches = 0.2 * (data_length - seq_len)
    # Solving: seq_len = data_length - (min_val_batches / 0.2)
    max_seq_len = data_length - (min_val_batches / 0.2)
    print(f"  For at least {min_val_batches} val batches: seq_len <= {int(max_seq_len)}")
