@echo off
echo ========================================
echo      V5 TRADER SYSTEM STARTUP
echo ========================================

echo Step 1: Aktiviere Virtual Environment
call venv\Scripts\activate
if errorlevel 1 (
    echo ERROR: Virtual Environment nicht gefunden!
    echo Bitte zuerst 'python -m venv venv' ausfuehren
    pause
    exit /b 1
)

echo Step 2: Installiere/Update Dependencies
pip install -q websockets redis psycopg2-binary
if errorlevel 1 (
    echo WARNING: Einige Dependencies konnten nicht installiert werden
)

echo Step 3: Starte Docker Services (falls verfuegbar)
docker-compose up -d 2>nul
if errorlevel 1 (
    echo INFO: Docker nicht verfuegbar oder docker-compose.yml nicht gefunden
    echo System laeuft ohne Docker Services
)

echo Step 4: Warte auf Services...
timeout /t 10 /nobreak >nul

echo Step 5: Starte System Tests
python tests/integration/test_system.py
if errorlevel 1 (
    echo WARNING: Einige Tests sind fehlgeschlagen
)

echo Step 6: Starte WebSocket Server (im Hintergrund)
start "WebSocket Server" python services/websocket_server.py

echo Step 7: Starte System Monitor
start "System Monitor" python tools/system_monitor.py

echo.
echo ========================================
echo         SYSTEM ERFOLGREICH GESTARTET
echo ========================================
echo.
echo Verfuegbare URLs:
echo - WebSocket Server: ws://localhost:8765
echo - MLflow UI: http://localhost:5000
echo - Grafana: http://localhost:3000
echo.
echo Verfuegbare Tools:
echo - System Monitor: Laeuft bereits
echo - Trading Tests: python test_trading_strategy.py
echo - Model Training: python train_and_register.py
echo.
echo Druecke eine beliebige Taste zum Beenden...
pause >nul

echo.
echo Beende System...
taskkill /f /im python.exe /fi "WINDOWTITLE eq WebSocket Server*" 2>nul
taskkill /f /im python.exe /fi "WINDOWTITLE eq System Monitor*" 2>nul
docker-compose down 2>nul

echo System beendet!
pause
