#!/usr/bin/env python3
"""
Professional Backtesting Engine for V5_Trader
Supports vectorized backtesting with realistic transaction costs
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Callable
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

from .portfolio import Portfolio
from .metrics import PerformanceMetrics

logger = logging.getLogger(__name__)

@dataclass
class BacktestConfig:
    """Backtesting configuration"""
    initial_capital: float = 100000.0
    commission: float = 0.001  # 0.1%
    slippage: float = 0.0005   # 0.05%
    margin_requirement: float = 0.25  # 25% for margin trading
    max_leverage: float = 4.0
    risk_free_rate: float = 0.02  # 2% annual
    benchmark_symbol: str = "SPY"

class BacktestEngine:
    """
    Professional backtesting engine with realistic market simulation
    """
    
    def __init__(self, config: BacktestConfig = None):
        self.config = config or BacktestConfig()
        self.portfolio = Portfolio(self.config.initial_capital)
        self.metrics = PerformanceMetrics()
        self.results = {}
        self.trades = []
        
    def run_backtest(
        self,
        strategy: Callable,
        data: pd.DataFrame,
        start_date: str = None,
        end_date: str = None,
        rebalance_frequency: str = 'D'
    ) -> Dict:
        """
        Run comprehensive backtest
        
        Args:
            strategy: Trading strategy function
            data: Market data with OHLCV columns
            start_date: Backtest start date
            end_date: Backtest end date
            rebalance_frequency: Rebalancing frequency ('D', 'W', 'M')
            
        Returns:
            Comprehensive backtest results
        """
        logger.info("🚀 Starting backtest...")
        
        # Prepare data
        test_data = self._prepare_data(data, start_date, end_date)
        
        # Initialize tracking
        portfolio_values = []
        positions = []
        trades = []
        
        # Run backtest
        for i, (date, row) in enumerate(test_data.iterrows()):
            # Generate signals
            signals = strategy(test_data.iloc[:i+1])
            
            # Execute trades
            if signals is not None:
                executed_trades = self._execute_signals(signals, row, date)
                trades.extend(executed_trades)
            
            # Update portfolio
            self.portfolio.update_market_value(row)
            portfolio_values.append({
                'date': date,
                'portfolio_value': self.portfolio.total_value,
                'cash': self.portfolio.cash,
                'positions_value': self.portfolio.positions_value
            })
            
            # Track positions
            positions.append({
                'date': date,
                'positions': self.portfolio.positions.copy()
            })
        
        # Calculate performance metrics
        portfolio_df = pd.DataFrame(portfolio_values).set_index('date')
        benchmark_returns = self._calculate_benchmark_returns(test_data)
        
        results = self._calculate_results(
            portfolio_df, 
            trades, 
            benchmark_returns,
            test_data
        )
        
        logger.info("✅ Backtest completed successfully")
        return results
    
    def _prepare_data(self, data: pd.DataFrame, start_date: str, end_date: str) -> pd.DataFrame:
        """Prepare and validate data for backtesting"""
        # Ensure datetime index
        if not isinstance(data.index, pd.DatetimeIndex):
            data.index = pd.to_datetime(data.index)
        
        # Filter date range
        if start_date:
            data = data[data.index >= start_date]
        if end_date:
            data = data[data.index <= end_date]
        
        # Validate required columns
        required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Forward fill missing values
        data = data.fillna(method='ffill')
        
        return data
    
    def _execute_signals(self, signals: Dict, market_data: pd.Series, date: datetime) -> List[Dict]:
        """Execute trading signals with realistic transaction costs"""
        executed_trades = []
        
        for symbol, signal in signals.items():
            if signal == 0:  # No action
                continue
                
            # Get current price (use Close price)
            price = market_data.get('Close', market_data.get(f'{symbol}_Close'))
            if pd.isna(price):
                continue
            
            # Apply slippage
            if signal > 0:  # Buy
                execution_price = price * (1 + self.config.slippage)
            else:  # Sell
                execution_price = price * (1 - self.config.slippage)
            
            # Calculate position size
            position_size = self._calculate_position_size(signal, execution_price)
            
            if position_size != 0:
                # Execute trade
                trade = self.portfolio.execute_trade(
                    symbol=symbol,
                    quantity=position_size,
                    price=execution_price,
                    commission=self.config.commission
                )
                
                if trade:
                    trade['date'] = date
                    trade['signal'] = signal
                    executed_trades.append(trade)
        
        return executed_trades
    
    def _calculate_position_size(self, signal: float, price: float) -> int:
        """Calculate position size based on signal strength and risk management"""
        # Simple position sizing - can be enhanced
        max_position_value = self.portfolio.total_value * 0.1  # Max 10% per position
        
        if signal > 0:  # Buy signal
            available_cash = self.portfolio.cash
            position_value = min(max_position_value, available_cash * abs(signal))
            return int(position_value / price)
        else:  # Sell signal
            # For now, sell all positions (can be enhanced for partial sells)
            return -self.portfolio.positions.get(signal, 0)
    
    def _calculate_benchmark_returns(self, data: pd.DataFrame) -> pd.Series:
        """Calculate benchmark returns for comparison"""
        # Use Close prices for benchmark
        if 'Close' in data.columns:
            prices = data['Close']
        else:
            # Use first available price column
            price_cols = [col for col in data.columns if 'Close' in col]
            if price_cols:
                prices = data[price_cols[0]]
            else:
                # Fallback to simple index
                prices = pd.Series(range(len(data)), index=data.index)
        
        return prices.pct_change().fillna(0)
    
    def _calculate_results(
        self, 
        portfolio_df: pd.DataFrame, 
        trades: List[Dict],
        benchmark_returns: pd.Series,
        market_data: pd.DataFrame
    ) -> Dict:
        """Calculate comprehensive backtest results"""
        
        # Portfolio returns
        portfolio_returns = portfolio_df['portfolio_value'].pct_change().fillna(0)
        
        # Performance metrics
        metrics = self.metrics.calculate_metrics(
            portfolio_returns,
            benchmark_returns,
            self.config.risk_free_rate
        )
        
        # Trade analysis
        trade_analysis = self._analyze_trades(trades)
        
        # Drawdown analysis
        drawdown_analysis = self._calculate_drawdowns(portfolio_df['portfolio_value'])
        
        results = {
            'performance_metrics': metrics,
            'trade_analysis': trade_analysis,
            'drawdown_analysis': drawdown_analysis,
            'portfolio_evolution': portfolio_df,
            'trades': trades,
            'final_portfolio_value': portfolio_df['portfolio_value'].iloc[-1],
            'total_return': (portfolio_df['portfolio_value'].iloc[-1] / self.config.initial_capital - 1) * 100,
            'config': self.config
        }
        
        return results
    
    def _analyze_trades(self, trades: List[Dict]) -> Dict:
        """Analyze trading performance"""
        if not trades:
            return {'total_trades': 0}
        
        trades_df = pd.DataFrame(trades)
        
        # Basic trade statistics
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['pnl'] > 0])
        losing_trades = len(trades_df[trades_df['pnl'] < 0])
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        avg_win = trades_df[trades_df['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
        avg_loss = trades_df[trades_df['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
        
        profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if avg_loss != 0 else float('inf')
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'total_pnl': trades_df['pnl'].sum(),
            'total_commission': trades_df['commission'].sum()
        }
    
    def _calculate_drawdowns(self, portfolio_values: pd.Series) -> Dict:
        """Calculate drawdown statistics"""
        # Calculate running maximum
        running_max = portfolio_values.expanding().max()
        
        # Calculate drawdown
        drawdown = (portfolio_values - running_max) / running_max
        
        # Maximum drawdown
        max_drawdown = drawdown.min()
        
        # Drawdown duration
        drawdown_periods = (drawdown < 0).astype(int)
        drawdown_duration = drawdown_periods.groupby((drawdown_periods != drawdown_periods.shift()).cumsum()).sum().max()
        
        return {
            'max_drawdown': max_drawdown,
            'max_drawdown_duration': drawdown_duration,
            'current_drawdown': drawdown.iloc[-1],
            'drawdown_series': drawdown
        }
    
    def generate_report(self, results: Dict, save_path: str = None) -> str:
        """Generate comprehensive backtest report"""
        report = f"""
# 📊 V5_Trader Backtest Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 💰 Performance Summary
- **Total Return**: {results['total_return']:.2f}%
- **Final Portfolio Value**: ${results['final_portfolio_value']:,.2f}
- **Initial Capital**: ${self.config.initial_capital:,.2f}

## 📈 Risk Metrics
- **Sharpe Ratio**: {results['performance_metrics'].get('sharpe_ratio', 'N/A'):.3f}
- **Maximum Drawdown**: {results['drawdown_analysis']['max_drawdown']:.2%}
- **Volatility**: {results['performance_metrics'].get('volatility', 'N/A'):.2%}

## 🎯 Trading Statistics
- **Total Trades**: {results['trade_analysis']['total_trades']}
- **Win Rate**: {results['trade_analysis']['win_rate']:.2%}
- **Profit Factor**: {results['trade_analysis']['profit_factor']:.2f}
- **Average Win**: ${results['trade_analysis']['avg_win']:.2f}
- **Average Loss**: ${results['trade_analysis']['avg_loss']:.2f}

## ⚙️ Configuration
- **Commission**: {self.config.commission:.3%}
- **Slippage**: {self.config.slippage:.3%}
- **Initial Capital**: ${self.config.initial_capital:,.2f}
        """
        
        if save_path:
            with open(save_path, 'w') as f:
                f.write(report)
            logger.info(f"Report saved to {save_path}")
        
        return report
