#!/usr/bin/env python3
"""
Clean, configuration-based training script for V5_Trader
Uses structured logging and modular configuration
"""
import logging
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional

import torch
import pytorch_lightning as pl
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping
from pytorch_lightning.loggers import MLFlowLogger
import mlflow

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from config.config_loader import get_config_loader, get_model_config, get_mlflow_config, get_environment_config
from data.fin_data_module import FinancialDataModule
from models.ensemble.advanced_transformer import AdvancedFinancialTransformer

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/training.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ModelTrainer:
    """Clean model trainer with configuration management"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_loader = get_config_loader(config_path)
        self.model_config = get_model_config()
        self.mlflow_config = get_mlflow_config()
        self.env_config = get_environment_config()
        
        # Validate configuration
        if not self.config_loader.validate_config():
            raise ValueError("Configuration validation failed")
        
        logger.info("✅ Configuration loaded and validated")
        
    def setup_mlflow(self):
        """Setup MLflow tracking"""
        try:
            mlflow.set_tracking_uri(self.mlflow_config.tracking_uri)
            
            # Set or create experiment
            try:
                experiment = mlflow.get_experiment_by_name(self.mlflow_config.experiment_name)
                if experiment is None:
                    experiment_id = mlflow.create_experiment(self.mlflow_config.experiment_name)
                    logger.info(f"📊 Created new MLflow experiment: {self.mlflow_config.experiment_name}")
                else:
                    experiment_id = experiment.experiment_id
                    logger.info(f"📊 Using existing MLflow experiment: {self.mlflow_config.experiment_name}")
                
                mlflow.set_experiment(experiment_id=experiment_id)
                
            except Exception as e:
                logger.warning(f"⚠️ MLflow experiment setup failed: {e}")
                
        except Exception as e:
            logger.error(f"❌ MLflow setup failed: {e}")
            raise
    
    def create_data_module(self) -> FinancialDataModule:
        """Create data module with configuration"""
        logger.info("📊 Creating data module...")
        
        data_module = FinancialDataModule(
            data_path="data/market_data.csv",
            sequence_length=self.model_config.sequence_length,
            batch_size=self.model_config.batch_size,
            val_split=self.model_config.val_split,
            test_split=self.model_config.test_split,
            num_workers=self.env_config.num_workers
        )
        
        logger.info(f"✅ Data module created with sequence length: {self.model_config.sequence_length}")
        return data_module
    
    def create_model(self, input_features: int) -> AdvancedFinancialTransformer:
        """Create model with configuration"""
        logger.info("🧠 Creating model...")
        
        # Convert config to parameters dict
        params = {
            "input_features": input_features,
            "sequence_length": self.model_config.sequence_length,
            "d_model": self.model_config.d_model,
            "n_heads": self.model_config.n_heads,
            "n_encoder_layers": self.model_config.n_encoder_layers,
            "dropout": self.model_config.dropout,
            "cnn_filters": self.model_config.cnn_filters,
            "lstm_units": self.model_config.lstm_units,
            "output_size": self.model_config.output_size,
            "learning_rate": self.model_config.learning_rate,
            "weight_decay": self.model_config.weight_decay,
            "use_mixed_precision": self.model_config.use_mixed_precision,
            "uncertainty_estimation": self.model_config.uncertainty_estimation,
            "ensemble_size": self.model_config.ensemble_size
        }
        
        model = AdvancedFinancialTransformer(**params)
        
        # Log model info
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        logger.info(f"✅ Model created:")
        logger.info(f"   Total parameters: {total_params:,}")
        logger.info(f"   Trainable parameters: {trainable_params:,}")
        logger.info(f"   Model size: {total_params * 4 / 1024 / 1024:.1f} MB")
        
        return model
    
    def setup_callbacks(self) -> list:
        """Setup training callbacks"""
        callbacks = []
        
        # Model checkpoint
        checkpoint_callback = ModelCheckpoint(
            dirpath="checkpoints",
            filename=f"{self.model_config.name}-{{epoch:02d}}-{{val_loss:.2f}}",
            monitor="val_loss",
            mode="min",
            save_top_k=3,
            save_last=True,
            verbose=True
        )
        callbacks.append(checkpoint_callback)
        
        # Early stopping
        early_stopping = EarlyStopping(
            monitor="val_loss",
            patience=self.model_config.patience,
            mode="min",
            min_delta=self.model_config.min_delta,
            verbose=True
        )
        callbacks.append(early_stopping)
        
        logger.info("✅ Training callbacks configured")
        return callbacks
    
    def setup_trainer(self, callbacks: list) -> pl.Trainer:
        """Setup PyTorch Lightning trainer"""
        
        # Setup MLflow logger
        mlflow_logger = MLFlowLogger(
            experiment_name=self.mlflow_config.experiment_name,
            tracking_uri=self.mlflow_config.tracking_uri,
            run_name=f"{self.mlflow_config.run_name_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        
        # Determine device
        if self.env_config.device == "auto":
            accelerator = "gpu" if torch.cuda.is_available() else "cpu"
            devices = 1 if torch.cuda.is_available() else None
        elif self.env_config.device == "cuda":
            accelerator = "gpu"
            devices = 1
        else:
            accelerator = "cpu"
            devices = None
        
        # Setup trainer
        trainer = pl.Trainer(
            max_epochs=self.model_config.max_epochs,
            callbacks=callbacks,
            logger=mlflow_logger,
            accelerator=accelerator,
            devices=devices,
            precision=16 if self.model_config.use_mixed_precision else 32,
            gradient_clip_val=self.model_config.gradient_clip_val,
            log_every_n_steps=10,
            enable_progress_bar=True,
            enable_model_summary=True
        )
        
        logger.info(f"✅ Trainer configured:")
        logger.info(f"   Accelerator: {accelerator}")
        logger.info(f"   Devices: {devices}")
        logger.info(f"   Precision: {16 if self.model_config.use_mixed_precision else 32}")
        logger.info(f"   Max epochs: {self.model_config.max_epochs}")
        
        return trainer
    
    def train(self) -> str:
        """Execute training pipeline"""
        logger.info("🚀 Starting training pipeline...")
        
        try:
            # Setup MLflow
            self.setup_mlflow()
            
            # Create data module
            data_module = self.create_data_module()
            data_module.setup()
            
            # Get input features from data
            sample_batch = next(iter(data_module.train_dataloader()))
            input_features = sample_batch[0].shape[-1]
            
            # Create model
            model = self.create_model(input_features)
            
            # Setup training components
            callbacks = self.setup_callbacks()
            trainer = self.setup_trainer(callbacks)
            
            # Start MLflow run
            with mlflow.start_run() as run:
                # Log configuration
                if self.mlflow_config.log_params:
                    mlflow.log_params({
                        "model_name": self.model_config.name,
                        "model_version": self.model_config.version,
                        "sequence_length": self.model_config.sequence_length,
                        "d_model": self.model_config.d_model,
                        "n_heads": self.model_config.n_heads,
                        "n_encoder_layers": self.model_config.n_encoder_layers,
                        "learning_rate": self.model_config.learning_rate,
                        "batch_size": self.model_config.batch_size,
                        "input_features": input_features
                    })
                
                # Train model
                logger.info("🎯 Starting model training...")
                trainer.fit(model, data_module)
                
                # Test model
                logger.info("🧪 Testing model...")
                test_results = trainer.test(model, data_module)
                
                # Log test results
                if self.mlflow_config.log_metrics and test_results:
                    for key, value in test_results[0].items():
                        mlflow.log_metric(f"final_{key}", value)
                
                # Register model
                if self.mlflow_config.log_model:
                    logger.info("📝 Registering model...")
                    
                    # Log model
                    mlflow.pytorch.log_model(
                        model,
                        "model",
                        registered_model_name=self.model_config.name
                    )
                    
                    # Transition to staging
                    client = mlflow.tracking.MlflowClient()
                    latest_version = client.get_latest_versions(
                        self.model_config.name, stages=["None"]
                    )[0]
                    
                    client.transition_model_version_stage(
                        name=self.model_config.name,
                        version=latest_version.version,
                        stage="Staging"
                    )
                    
                    logger.info(f"✅ Model registered as {self.model_config.name} v{latest_version.version}")
                
                run_id = run.info.run_id
                logger.info(f"✅ Training completed successfully! Run ID: {run_id}")
                
                return run_id
                
        except Exception as e:
            logger.error(f"❌ Training failed: {e}")
            raise

def main():
    """Main training function"""
    logger.info("🚀 V5_Trader Model Training Started")
    logger.info("=" * 60)
    
    try:
        # Create trainer
        trainer = ModelTrainer()
        
        # Execute training
        run_id = trainer.train()
        
        logger.info("=" * 60)
        logger.info("🎉 TRAINING COMPLETED SUCCESSFULLY!")
        logger.info(f"📊 MLflow Run ID: {run_id}")
        logger.info(f"🌐 MLflow UI: {trainer.mlflow_config.tracking_uri}")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error("=" * 60)
        logger.error("❌ TRAINING FAILED!")
        logger.error(f"Error: {e}")
        logger.error("=" * 60)
        sys.exit(1)

if __name__ == "__main__":
    main()
