import requests
import time
import os
from datetime import datetime
from typing import Dict, List

class SystemMonitor:
    """Überwacht alle System-Komponenten"""
    
    def __init__(self):
        self.services = {
            "API Gateway": "http://localhost:8000/health",
            "MLflow": "http://localhost:5000",
            "Grafana": "http://localhost:3000"
        }
        
    def check_http_service(self, name: str, url: str) -> Dict:
        """Überprüft HTTP-basierte Services"""
        try:
            response = requests.get(url, timeout=5)
            return {
                "service": name,
                "status": "✅ UP" if response.status_code == 200 else "⚠️ ISSUES",
                "response_time": response.elapsed.total_seconds(),
                "status_code": response.status_code
            }
        except Exception as e:
            return {
                "service": name,
                "status": "❌ DOWN",
                "error": str(e),
                "response_time": None,
                "status_code": None
            }
    
    def check_database(self) -> Dict:
        """Überprüft PostgreSQL Datenbank"""
        try:
            import psycopg2
            conn = psycopg2.connect(
                host="localhost",
                database="v5trader",
                user="postgres",
                password=os.getenv("POSTGRES_PASSWORD", "postgres")
            )
            conn.close()
            return {"service": "PostgreSQL", "status": "✅ UP"}
        except ImportError:
            return {"service": "PostgreSQL", "status": "⚠️ DRIVER MISSING", "error": "psycopg2 not installed"}
        except Exception as e:
            return {"service": "PostgreSQL", "status": "❌ DOWN", "error": str(e)}
    
    def check_redis(self) -> Dict:
        """Überprüft Redis"""
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, decode_responses=True)
            r.ping()
            return {"service": "Redis", "status": "✅ UP"}
        except ImportError:
            return {"service": "Redis", "status": "⚠️ DRIVER MISSING", "error": "redis not installed"}
        except Exception as e:
            return {"service": "Redis", "status": "❌ DOWN", "error": str(e)}
    
    def check_file_system(self) -> Dict:
        """Überprüft wichtige Dateien und Verzeichnisse"""
        critical_paths = [
            "data/market_data.csv",
            "models/",
            "logs/",
            "venv/"
        ]
        
        missing_paths = []
        for path in critical_paths:
            if not os.path.exists(path):
                missing_paths.append(path)
        
        if missing_paths:
            return {
                "service": "File System",
                "status": "⚠️ ISSUES",
                "error": f"Missing: {', '.join(missing_paths)}"
            }
        else:
            return {"service": "File System", "status": "✅ UP"}
    
    def generate_status_report(self) -> List[Dict]:
        """Generiert kompletten Statusbericht"""
        results = []
        
        # HTTP Services prüfen
        for name, url in self.services.items():
            results.append(self.check_http_service(name, url))
        
        # Datenbank prüfen
        results.append(self.check_database())
        
        # Redis prüfen
        results.append(self.check_redis())
        
        # File System prüfen
        results.append(self.check_file_system())
        
        return results
    
    def print_status(self):
        """Gibt Status auf der Konsole aus"""
        print(f"\n{'='*60}")
        print(f"V5 TRADER SYSTEM STATUS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")
        
        results = self.generate_status_report()
        
        for result in results:
            service = result['service'].ljust(15)
            status = result['status']
            
            if 'response_time' in result and result['response_time']:
                rt = f"({result['response_time']:.3f}s)"
            else:
                rt = ""
            
            print(f"{service}: {status} {rt}")
            
            if 'error' in result:
                print(f"   Error: {result['error']}")
        
        print(f"{'='*60}\n")
    
    def get_system_health_score(self) -> float:
        """Berechnet einen Gesundheitsscore für das System (0-100)"""
        results = self.generate_status_report()
        total_services = len(results)
        healthy_services = sum(1 for r in results if "✅" in r['status'])
        
        return (healthy_services / total_services) * 100


def continuous_monitoring():
    """Kontinuierliche Überwachung alle 30 Sekunden"""
    monitor = SystemMonitor()
    
    print("🔍 Starte kontinuierliche System-Überwachung...")
    print("Drücke Ctrl+C zum Beenden")
    
    try:
        while True:
            monitor.print_status()
            health_score = monitor.get_system_health_score()
            print(f"System Health Score: {health_score:.1f}%")
            time.sleep(30)
    except KeyboardInterrupt:
        print("\n🛑 Monitoring beendet!")


if __name__ == "__main__":
    continuous_monitoring()
