#!/usr/bin/env python3
"""
Test script for V5_Trader MLflow, Prometheus, and Grafana integration
Validates the complete monitoring and ML operations pipeline
"""
import os
import sys
import time
import asyncio
import requests
import subprocess
from pathlib import Path
from datetime import datetime
import json

class MonitoringIntegrationTest:
    """Test suite for monitoring integration"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.services = {
            "postgres": "http://localhost:5432",
            "redis": "redis://localhost:6379",
            "mlflow": "http://localhost:5000",
            "prometheus": "http://localhost:9090",
            "grafana": "http://localhost:3000",
            "api_gateway": "http://localhost:8000"
        }
        self.test_results = {}
    
    def test_docker_services(self):
        """Test if Docker services are running"""
        print("🐳 Testing Docker services...")
        
        try:
            # Check if docker-compose is available
            result = subprocess.run(['docker-compose', 'ps'], 
                                  capture_output=True, text=True, 
                                  cwd=self.project_root)
            
            if result.returncode == 0:
                running_services = []
                for line in result.stdout.split('\n'):
                    if 'Up' in line:
                        service_name = line.split()[0]
                        running_services.append(service_name)
                
                print(f"  ✅ Running services: {', '.join(running_services)}")
                self.test_results["docker_services"] = True
                return True
            else:
                print("  ❌ Docker Compose not running")
                self.test_results["docker_services"] = False
                return False
                
        except Exception as e:
            print(f"  ❌ Docker test failed: {e}")
            self.test_results["docker_services"] = False
            return False
    
    def test_mlflow_integration(self):
        """Test MLflow tracking server"""
        print("🧪 Testing MLflow integration...")
        
        try:
            # Test MLflow API
            response = requests.get(f"{self.services['mlflow']}/api/2.0/mlflow/experiments/list", 
                                  timeout=10)
            
            if response.status_code == 200:
                experiments = response.json()
                print(f"  ✅ MLflow API working - {len(experiments.get('experiments', []))} experiments")
                
                # Test MLflow UI
                ui_response = requests.get(self.services['mlflow'], timeout=10)
                if ui_response.status_code == 200:
                    print("  ✅ MLflow UI accessible")
                    self.test_results["mlflow"] = True
                    return True
                else:
                    print("  ⚠️ MLflow API working but UI not accessible")
                    self.test_results["mlflow"] = False
                    return False
            else:
                print(f"  ❌ MLflow API error: {response.status_code}")
                self.test_results["mlflow"] = False
                return False
                
        except Exception as e:
            print(f"  ❌ MLflow test failed: {e}")
            self.test_results["mlflow"] = False
            return False
    
    def test_prometheus_integration(self):
        """Test Prometheus monitoring"""
        print("📊 Testing Prometheus integration...")
        
        try:
            # Test Prometheus API
            response = requests.get(f"{self.services['prometheus']}/api/v1/targets", 
                                  timeout=10)
            
            if response.status_code == 200:
                targets = response.json()
                active_targets = targets.get('data', {}).get('activeTargets', [])
                
                healthy_targets = [t for t in active_targets if t.get('health') == 'up']
                print(f"  ✅ Prometheus API working - {len(healthy_targets)}/{len(active_targets)} targets healthy")
                
                # Test specific metrics
                metrics_response = requests.get(
                    f"{self.services['prometheus']}/api/v1/query?query=up", 
                    timeout=10
                )
                
                if metrics_response.status_code == 200:
                    print("  ✅ Prometheus metrics accessible")
                    self.test_results["prometheus"] = True
                    return True
                else:
                    print("  ⚠️ Prometheus targets working but metrics not accessible")
                    self.test_results["prometheus"] = False
                    return False
            else:
                print(f"  ❌ Prometheus API error: {response.status_code}")
                self.test_results["prometheus"] = False
                return False
                
        except Exception as e:
            print(f"  ❌ Prometheus test failed: {e}")
            self.test_results["prometheus"] = False
            return False
    
    def test_grafana_integration(self):
        """Test Grafana dashboards"""
        print("📈 Testing Grafana integration...")
        
        try:
            # Test Grafana API (without auth for basic check)
            response = requests.get(f"{self.services['grafana']}/api/health", 
                                  timeout=10)
            
            if response.status_code == 200:
                health = response.json()
                print(f"  ✅ Grafana health: {health.get('database', 'unknown')}")
                
                # Test Grafana UI
                ui_response = requests.get(self.services['grafana'], timeout=10)
                if ui_response.status_code == 200:
                    print("  ✅ Grafana UI accessible")
                    self.test_results["grafana"] = True
                    return True
                else:
                    print("  ⚠️ Grafana API working but UI not accessible")
                    self.test_results["grafana"] = False
                    return False
            else:
                print(f"  ❌ Grafana API error: {response.status_code}")
                self.test_results["grafana"] = False
                return False
                
        except Exception as e:
            print(f"  ❌ Grafana test failed: {e}")
            self.test_results["grafana"] = False
            return False
    
    def test_api_gateway_metrics(self):
        """Test API Gateway Prometheus metrics"""
        print("🌐 Testing API Gateway metrics...")
        
        try:
            # Test API Gateway health
            response = requests.get(f"{self.services['api_gateway']}/health", 
                                  timeout=10)
            
            if response.status_code == 200:
                health = response.json()
                print(f"  ✅ API Gateway health: {health.get('status', 'unknown')}")
                
                # Test Prometheus metrics endpoint
                metrics_response = requests.get(f"{self.services['api_gateway']}/metrics", 
                                              timeout=10)
                
                if metrics_response.status_code == 200:
                    metrics_text = metrics_response.text
                    
                    # Check for key metrics
                    expected_metrics = [
                        "http_requests_total",
                        "http_request_duration_seconds",
                        "active_connections"
                    ]
                    
                    found_metrics = []
                    for metric in expected_metrics:
                        if metric in metrics_text:
                            found_metrics.append(metric)
                    
                    print(f"  ✅ Metrics endpoint working - {len(found_metrics)}/{len(expected_metrics)} metrics found")
                    self.test_results["api_gateway_metrics"] = True
                    return True
                else:
                    print("  ⚠️ API Gateway working but metrics endpoint not accessible")
                    self.test_results["api_gateway_metrics"] = False
                    return False
            else:
                print(f"  ❌ API Gateway error: {response.status_code}")
                self.test_results["api_gateway_metrics"] = False
                return False
                
        except Exception as e:
            print(f"  ❌ API Gateway test failed: {e}")
            self.test_results["api_gateway_metrics"] = False
            return False
    
    def test_end_to_end_flow(self):
        """Test complete monitoring flow"""
        print("🔄 Testing end-to-end monitoring flow...")
        
        try:
            # Make API request to generate metrics
            response = requests.get(f"{self.services['api_gateway']}/", timeout=10)
            
            if response.status_code == 200:
                print("  ✅ API request successful")
                
                # Wait for metrics to be scraped
                time.sleep(5)
                
                # Check if metrics appear in Prometheus
                prometheus_query = f"{self.services['prometheus']}/api/v1/query?query=http_requests_total"
                prom_response = requests.get(prometheus_query, timeout=10)
                
                if prom_response.status_code == 200:
                    data = prom_response.json()
                    if data.get('data', {}).get('result'):
                        print("  ✅ Metrics visible in Prometheus")
                        self.test_results["end_to_end"] = True
                        return True
                    else:
                        print("  ⚠️ No metrics data in Prometheus")
                        self.test_results["end_to_end"] = False
                        return False
                else:
                    print("  ❌ Prometheus query failed")
                    self.test_results["end_to_end"] = False
                    return False
            else:
                print("  ❌ API request failed")
                self.test_results["end_to_end"] = False
                return False
                
        except Exception as e:
            print(f"  ❌ End-to-end test failed: {e}")
            self.test_results["end_to_end"] = False
            return False
    
    def run_all_tests(self):
        """Run all monitoring integration tests"""
        print("🚀 V5_TRADER MONITORING INTEGRATION TEST")
        print("=" * 60)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        tests = [
            ("Docker Services", self.test_docker_services),
            ("MLflow Integration", self.test_mlflow_integration),
            ("Prometheus Integration", self.test_prometheus_integration),
            ("Grafana Integration", self.test_grafana_integration),
            ("API Gateway Metrics", self.test_api_gateway_metrics),
            ("End-to-End Flow", self.test_end_to_end_flow),
        ]
        
        results = []
        
        for test_name, test_func in tests:
            print(f"\n🧪 Running {test_name} test...")
            try:
                success = test_func()
                results.append((test_name, success))
            except Exception as e:
                print(f"❌ {test_name} test crashed: {e}")
                results.append((test_name, False))
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 MONITORING INTEGRATION TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for _, success in results if success)
        total = len(results)
        
        for test_name, success in results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{test_name:<25}: {status}")
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("\n🎉 MONITORING INTEGRATION SUCCESSFUL!")
            print("🚀 V5_Trader monitoring stack is fully operational!")
            
            print("\n📋 Available Interfaces:")
            print("  MLflow UI:     http://localhost:5000")
            print("  Prometheus:    http://localhost:9090")
            print("  Grafana:       http://localhost:3000")
            print("  API Gateway:   http://localhost:8000")
            print("  API Docs:      http://localhost:8000/docs")
            print("  Metrics:       http://localhost:8000/metrics")
            
            return True
        else:
            print(f"\n❌ MONITORING INTEGRATION FAILED!")
            print(f"Please fix the {total - passed} failing test(s) before proceeding.")
            return False


def main():
    """Main entry point"""
    tester = MonitoringIntegrationTest()
    
    try:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)


if __name__ == "__main__":
    main()
