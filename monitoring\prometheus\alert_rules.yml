groups:
  - name: v5trader_alerts
    rules:
      # API Gateway Alerts
      - alert: HighRequestLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="v5trader-api-gateway"}[5m])) > 0.5
        for: 2m
        labels:
          severity: warning
          service: api-gateway
        annotations:
          summary: "High request latency detected"
          description: "95th percentile latency is {{ $value }}s for API Gateway"

      - alert: HighErrorRate
        expr: rate(http_requests_total{job="v5trader-api-gateway",status=~"5.."}[5m]) / rate(http_requests_total{job="v5trader-api-gateway"}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
          service: api-gateway
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for API Gateway"

      - alert: APIGatewayDown
        expr: up{job="v5trader-api-gateway"} == 0
        for: 1m
        labels:
          severity: critical
          service: api-gateway
        annotations:
          summary: "API Gateway is down"
          description: "API Gateway has been down for more than 1 minute"

      # Database Alerts
      - alert: PostgreSQLDown
        expr: up{job="v5trader-postgres"} == 0
        for: 1m
        labels:
          severity: critical
          service: postgres
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL database has been down for more than 1 minute"

      - alert: HighDatabaseConnections
        expr: pg_stat_database_numbackends{job="v5trader-postgres"} > 80
        for: 5m
        labels:
          severity: warning
          service: postgres
        annotations:
          summary: "High number of database connections"
          description: "Database has {{ $value }} active connections"

      # Redis Alerts
      - alert: RedisDown
        expr: up{job="v5trader-redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis is down"
          description: "Redis cache has been down for more than 1 minute"

      - alert: HighRedisMemoryUsage
        expr: redis_memory_used_bytes{job="v5trader-redis"} / redis_memory_max_bytes{job="v5trader-redis"} > 0.9
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "High Redis memory usage"
          description: "Redis memory usage is {{ $value | humanizePercentage }}"

      # MLflow Alerts
      - alert: MLflowDown
        expr: up{job="v5trader-mlflow"} == 0
        for: 2m
        labels:
          severity: warning
          service: mlflow
        annotations:
          summary: "MLflow is down"
          description: "MLflow tracking server has been down for more than 2 minutes"

      # System Alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle",job="v5trader-system"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes{job="v5trader-system"} / node_memory_MemTotal_bytes{job="v5trader-system"})) * 100 > 90
        for: 5m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: LowDiskSpace
        expr: (1 - (node_filesystem_avail_bytes{job="v5trader-system",fstype!="tmpfs"} / node_filesystem_size_bytes{job="v5trader-system",fstype!="tmpfs"})) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "Low disk space"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }} ({{ $labels.mountpoint }})"

      # Model Performance Alerts
      - alert: ModelPredictionErrors
        expr: rate(model_predictions_total{status="error"}[5m]) > 0.1
        for: 3m
        labels:
          severity: warning
          service: ml-model
        annotations:
          summary: "High model prediction error rate"
          description: "Model prediction error rate is {{ $value }} errors/sec"

      - alert: LowModelConfidence
        expr: avg_over_time(model_confidence[10m]) < 0.7
        for: 5m
        labels:
          severity: warning
          service: ml-model
        annotations:
          summary: "Low model confidence"
          description: "Average model confidence is {{ $value }} over the last 10 minutes"
