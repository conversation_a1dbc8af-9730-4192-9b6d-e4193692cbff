{"project_structure": {"d:/V5_Trader/": {"services/": {"api-gateway/": ["main.py", "requirements.txt", "Dockerfile"], "ml-inference/": ["inference_engine.py", "model_loader.py", "Dockerfile"], "data-ingestion/": ["market_data_pipeline.py", "data_processors.py"], "order-execution/": ["smart_order_engine.py", "mt5_connector.py"], "risk-management/": ["risk_controller.py", "portfolio_manager.py"], "monitoring/": ["health_check.py", "metrics_collector.py"]}, "models/": {"ensemble/": ["cnn_lstm_model.py", "transformer_model.py"], "reinforcement/": ["ppo_agent.py", "ddpg_agent.py"], "explainable/": ["shap_analyzer.py", "lime_explainer.py"]}, "strategies/": ["ensemble_deep_learning.py", "reinforcement_learning.py"], "pipelines/": ["training_pipeline.py", "feature_engineering.py"], "infrastructure/": {"kubernetes/": ["deployment.yaml", "service.yaml", "configmap.yaml"], "docker/": ["docker-compose.yml", "Dockerfile.base"], "ci-cd/": [".github/workflows/ci.yml", "deploy.yml"]}, "tests/": ["test_trading_engine.py", "test_ml_models.py"], "docs/": ["README.md", "API_DOCS.md"], "config/": ["config.yaml", "secrets.yaml"]}}, "technology_stack": {"programming_languages": ["Python 3.11", "JavaScript", "YAML"], "ml_frameworks": ["PyTorch", "Scikit-learn", "XGBoost", "Optuna"], "data_processing": ["<PERSON><PERSON>", "NumPy", "Apache Kafka", "Redis"], "web_frameworks": ["FastAPI", "React", "WebSockets"], "databases": ["PostgreSQL", "MongoDB", "InfluxDB"], "infrastructure": ["<PERSON>er", "Kubernetes", "<PERSON><PERSON><PERSON>"], "monitoring": ["Prometheus", "<PERSON><PERSON>", "ELK Stack"], "ci_cd": ["GitHub Actions", "<PERSON><PERSON>"], "trading_apis": ["MT5 Python API", "Alpha Vantage", "Yahoo Finance"]}, "free_apis_and_tools": {"market_data": [{"name": "Alpha Vantage", "limit": "25 calls/day", "coverage": "Global stocks, forex, crypto"}, {"name": "Yahoo Finance", "limit": "Unlimited", "coverage": "Global markets"}, {"name": "Financial Modeling Prep", "limit": "250 calls/day", "coverage": "US markets, fundamentals"}, {"name": "MT5 API", "limit": "Unlimited", "coverage": "Forex, CFDs via broker"}], "mlops_tools": [{"name": "MLflow", "features": "Experiment tracking, model registry"}, {"name": "DVC", "features": "Data versioning, pipeline management"}, {"name": "Kubeflow", "features": "ML workflows on Kubernetes"}, {"name": "Weights & Biases", "features": "Free tier for experiment tracking"}], "infrastructure": [{"name": "GitHub Actions", "limit": "2000 minutes/month free"}, {"name": "DigitalOcean Kubernetes", "cost": "$12/month per node"}, {"name": "<PERSON><PERSON>", "limit": "Unlimited public repos"}, {"name": "Prometheus + Grafana", "cost": "Free open-source"}]}}