import pandas as pd
import numpy as np

# Load the data
df = pd.read_csv("data/market_data.csv", parse_dates=["Date"])
print("Data shape:", df.shape)
print("Columns:", df.columns.tolist())
print("Data types:\n", df.dtypes)

# Check Target column
print("\n=== TARGET COLUMN ANALYSIS ===")
print("Target column exists:", "Target" in df.columns)
print("Target dtype:", df["Target"].dtype)
print("Target shape:", df["Target"].shape)
print("Target unique values:", df["Target"].unique())
print("Target value counts:\n", df["Target"].value_counts())

# Check for each ticker
print("\n=== TICKER-WISE TARGET ANALYSIS ===")
for ticker in df["Ticker"].unique():
    ticker_data = df[df["Ticker"] == ticker]
    print(f"\n{ticker}:")
    print(f"  Rows: {len(ticker_data)}")
    print(f"  Target values: {ticker_data['Target'].unique()}")
    print(f"  Target counts: {ticker_data['Target'].value_counts().to_dict()}")
    print(f"  NaN targets: {ticker_data['Target'].isna().sum()}")
    
    # Check if target calculation is correct
    closes = ticker_data["Close"].values
    expected_targets = (closes[1:] > closes[:-1]).astype(float)
    actual_targets = ticker_data["Target"].values[:-1]  # Exclude last row (should be NaN)
    
    print(f"  Target calculation correct: {np.allclose(expected_targets, actual_targets, equal_nan=True)}")

print("\n=== SAMPLE DATA ===")
print(df.head(10))
