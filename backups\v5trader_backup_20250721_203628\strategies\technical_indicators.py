import pandas as pd
import numpy as np
from typing import Dict, Tuple
from .base_strategy import BaseStrategy

class TechnicalIndicators:
    """Sammlung technischer Indikatoren für Trading-Strategien"""
    
    @staticmethod
    def calculate_sma(data: pd.Series, window: int) -> pd.Series:
        """Simple Moving Average"""
        return data.rolling(window=window).mean()
    
    @staticmethod
    def calculate_ema(data: pd.Series, window: int) -> pd.Series:
        """Exponential Moving Average"""
        return data.ewm(span=window).mean()
    
    @staticmethod
    def calculate_rsi(data: pd.Series, window: int = 14) -> pd.Series:
        """Relative Strength Index"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    @staticmethod
    def calculate_bollinger_bands(data: pd.Series, window: int = 20, 
                                  num_std: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """Bollinger Bands"""
        rolling_mean = data.rolling(window=window).mean()
        rolling_std = data.rolling(window=window).std()
        
        upper_band = rolling_mean + (rolling_std * num_std)
        lower_band = rolling_mean - (rolling_std * num_std)
        
        return upper_band, rolling_mean, lower_band
    
    @staticmethod
    def calculate_macd(data: pd.Series, fast: int = 12, slow: int = 26, 
                       signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """MACD (Moving Average Convergence Divergence)"""
        ema_fast = TechnicalIndicators.calculate_ema(data, fast)
        ema_slow = TechnicalIndicators.calculate_ema(data, slow)
        
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.calculate_ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return macd_line, signal_line, histogram


class SimpleMovingAverageStrategy(BaseStrategy):
    """Einfache Moving Average Crossover Strategie"""
    
    def __init__(self, parameters: Dict):
        super().__init__("SMA_Crossover", parameters)
        self.short_window = parameters.get("short_window", 10)
        self.long_window = parameters.get("long_window", 30)
        self.indicators = TechnicalIndicators()
        
    def generate_signals(self, data: pd.DataFrame) -> Dict:
        """Generiert Buy/Sell Signale basierend auf SMA Crossover"""
        if len(data) < self.long_window:
            return {"signal": "HOLD", "confidence": 0.0}
        
        # Berechne Moving Averages
        data['SMA_short'] = self.indicators.calculate_sma(data['Close'], self.short_window)
        data['SMA_long'] = self.indicators.calculate_sma(data['Close'], self.long_window)
        
        # Aktueller und vorheriger Wert
        current_short = data['SMA_short'].iloc[-1]
        current_long = data['SMA_long'].iloc[-1]
        prev_short = data['SMA_short'].iloc[-2]
        prev_long = data['SMA_long'].iloc[-2]
        
        # Signal generieren
        if pd.isna(current_short) or pd.isna(current_long):
            return {"signal": "HOLD", "confidence": 0.0}
        
        # Crossover Detection
        if prev_short <= prev_long and current_short > current_long:
            # Golden Cross - Buy Signal
            confidence = min(abs(current_short - current_long) / current_long, 1.0)
            return {"signal": "BUY", "confidence": confidence}
        elif prev_short >= prev_long and current_short < current_long:
            # Death Cross - Sell Signal
            confidence = min(abs(current_short - current_long) / current_long, 1.0)
            return {"signal": "SELL", "confidence": confidence}
        else:
            return {"signal": "HOLD", "confidence": 0.0}
    
    def calculate_position_size(self, signal: Dict, current_price: float) -> float:
        """Berechnet Positionsgröße basierend auf Risikoparametern"""
        if signal["signal"] == "HOLD":
            return 0.0
        
        # Einfache Positionsgröße: 10% des verfügbaren Kapitals
        position_value = self.balance * 0.1 * signal["confidence"]
        quantity = position_value / current_price
        
        return quantity


if __name__ == "__main__":
    # Test der Strategie
    strategy_params = {
        "short_window": 10,
        "long_window": 30
    }
    
    strategy = SimpleMovingAverageStrategy(strategy_params)
    print(f"Strategie {strategy.name} erstellt!")
