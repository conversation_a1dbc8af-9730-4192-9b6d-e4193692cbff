"""
End-to-end integration tests for the complete V5_Trader pipeline
"""
import os
import sys
import tempfile
import pandas as pd
import torch
from datetime import datetime, timedelta

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.ensemble.advanced_transformer import AdvancedFinancialTransformer
from data.fin_data_module import FinancialDataModule
import fetch_market_data


class TestEndToEnd:
    """End-to-end integration tests"""
    
    def setup_method(self):
        """Setup for end-to-end tests"""
        self.temp_dir = tempfile.mkdtemp()
        self.data_path = os.path.join(self.temp_dir, "test_market_data.csv")
        
    def create_mock_market_data(self):
        """Create mock market data for testing"""
        # Create 200 days of mock data for 2 tickers
        n_days = 200
        tickers = ["AAPL", "MSFT"]
        
        all_data = []
        for ticker in tickers:
            dates = pd.date_range('2023-01-01', periods=n_days, freq='D')
            
            # Generate realistic price data
            base_price = 100 if ticker == "AAPL" else 200
            price_walk = torch.cumsum(torch.randn(n_days) * 0.02, dim=0).numpy()
            
            data = pd.DataFrame({
                'Date': dates,
                'Open': base_price + price_walk + torch.randn(n_days).numpy() * 0.5,
                'High': base_price + price_walk + torch.randn(n_days).numpy() * 0.5 + 2,
                'Low': base_price + price_walk + torch.randn(n_days).numpy() * 0.5 - 2,
                'Close': base_price + price_walk,
                'Volume': 1000000 + torch.randint(0, 500000, (n_days,)).numpy(),
                'Ticker': ticker
            })
            
            # Ensure High >= Close >= Low and High >= Open >= Low
            data['High'] = data[['Open', 'High', 'Close']].max(axis=1) + abs(torch.randn(n_days).numpy() * 0.5)
            data['Low'] = data[['Open', 'Low', 'Close']].min(axis=1) - abs(torch.randn(n_days).numpy() * 0.5)
            
            all_data.append(data)
        
        # Combine data
        full_df = pd.concat(all_data, ignore_index=True)
        full_df.sort_values(["Ticker", "Date"], inplace=True)
        
        # Calculate targets
        full_df["Target"] = 0.0
        for ticker in tickers:
            mask = full_df["Ticker"] == ticker
            closes = full_df.loc[mask, "Close"]
            target_values = (closes.shift(-1) > closes).astype(float)
            full_df.loc[mask, "Target"] = target_values
        
        # Remove rows where Target is NaN
        full_df = full_df.dropna(subset=['Target'])
        
        # Save to CSV
        full_df.to_csv(self.data_path, index=False)
        return full_df
        
    def test_complete_pipeline(self):
        """Test the complete pipeline from data loading to model training"""
        print("🔄 Testing complete pipeline...")
        
        # 1. Create mock data
        print("📊 Creating mock market data...")
        df_raw = self.create_mock_market_data()
        assert len(df_raw) > 0
        print(f"   Created {len(df_raw)} data points")
        
        # 2. Load and process data (like in train_and_register.py)
        print("🔧 Processing data...")
        df_raw = pd.read_csv(self.data_path, parse_dates=["Date"])
        df_raw = df_raw.loc[:, ~df_raw.columns.duplicated()]
        
        # Filter AAPL data
        df_aapl = df_raw[df_raw["Ticker"] == "AAPL"].copy()
        df_aapl.set_index("Date", inplace=True)
        
        assert "Target" in df_aapl.columns
        assert len(df_aapl) > 120  # Ensure enough data for sequence length
        
        # 3. Prepare features and targets
        feature_cols = ["Open", "High", "Low", "Close", "Volume"]
        df_feats = df_aapl[feature_cols].copy()
        
        # Ensure numeric types
        for col in feature_cols:
            df_feats[col] = pd.to_numeric(df_feats[col], errors='coerce')
        
        df_feats = df_feats.dropna()
        targets = df_aapl.loc[df_feats.index, "Target"]
        
        print(f"   Features shape: {df_feats.shape}")
        print(f"   Targets shape: {targets.shape}")
        
        # 4. Create DataModule
        print("📦 Creating DataModule...")
        dm = FinancialDataModule(
            features=df_feats,
            targets=targets,
            seq_len=60,  # Smaller for testing
            batch_size=16,
            val_split=0.2
        )
        dm.setup()
        
        # Test data loaders
        train_loader = dm.train_dataloader()
        val_loader = dm.val_dataloader()
        
        train_batch = next(iter(train_loader))
        val_batch = next(iter(val_loader))
        
        x_train, y_train = train_batch
        x_val, y_val = val_batch
        
        assert x_train.shape == (16, 60, 5)
        assert y_train.shape == (16,)
        print(f"   Train batch shape: {x_train.shape}")
        print(f"   Val batch shape: {x_val.shape}")
        
        # 5. Create and test model
        print("🤖 Creating model...")
        params = {
            "input_features": df_feats.shape[1],
            "sequence_length": 60,
            "d_model": 128,  # Smaller for testing
            "n_heads": 4,
            "n_encoder_layers": 2,
            "dropout": 0.1,
            "cnn_filters": 64,
            "lstm_units": 128,
            "output_size": 1,
            "learning_rate": 1e-3,
            "weight_decay": 1e-5,
            "use_mixed_precision": False,  # Disable for testing
            "uncertainty_estimation": True,
            "ensemble_size": 3
        }
        
        model = AdvancedFinancialTransformer(**params)
        
        # 6. Test forward pass
        print("⚡ Testing forward pass...")
        model.eval()
        with torch.no_grad():
            outputs = model(x_train)
            
        assert 'short_term' in outputs
        assert outputs['short_term'].shape == (16, 1)
        print(f"   Model output shape: {outputs['short_term'].shape}")
        
        # 7. Test training step
        print("🏋️ Testing training step...")
        model.train()
        loss = model.training_step((x_train, y_train.unsqueeze(1)), 0)
        assert isinstance(loss, torch.Tensor)
        assert loss.item() > 0
        print(f"   Training loss: {loss.item():.4f}")
        
        # 8. Test validation step
        print("✅ Testing validation step...")
        model.eval()
        val_loss = model.validation_step((x_val, y_val.unsqueeze(1)), 0)
        assert isinstance(val_loss, torch.Tensor)
        print(f"   Validation loss: {val_loss.item():.4f}")
        
        # 9. Test uncertainty estimation
        print("🎯 Testing uncertainty estimation...")
        with torch.no_grad():
            uncertainty_outputs = model(x_val, return_uncertainty=True)
            
        if model.uncertainty_estimation:
            assert 'epistemic_uncertainty' in uncertainty_outputs
            print(f"   Uncertainty shape: {uncertainty_outputs['epistemic_uncertainty'].shape}")
        
        print("🎉 Complete pipeline test passed!")
        
    def test_data_quality_checks(self):
        """Test data quality and validation"""
        print("🔍 Testing data quality checks...")
        
        # Create data with potential issues
        df_raw = self.create_mock_market_data()
        
        # Check for required columns
        required_cols = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'Ticker', 'Target']
        for col in required_cols:
            assert col in df_raw.columns, f"Missing required column: {col}"
            
        # Check data types
        assert pd.api.types.is_datetime64_any_dtype(df_raw['Date'])
        assert pd.api.types.is_numeric_dtype(df_raw['Open'])
        assert pd.api.types.is_numeric_dtype(df_raw['Target'])
        
        # Check for NaN values in critical columns
        critical_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in critical_cols:
            nan_count = df_raw[col].isna().sum()
            assert nan_count == 0, f"Found {nan_count} NaN values in {col}"
            
        # Check price relationships (High >= Close >= Low, etc.)
        price_data = df_raw[['Open', 'High', 'Low', 'Close']]
        assert (df_raw['High'] >= df_raw['Close']).all(), "High should be >= Close"
        assert (df_raw['Close'] >= df_raw['Low']).all(), "Close should be >= Low"
        assert (df_raw['High'] >= df_raw['Open']).all(), "High should be >= Open"
        assert (df_raw['Open'] >= df_raw['Low']).all(), "Open should be >= Low"
        
        # Check target values are binary
        unique_targets = df_raw['Target'].dropna().unique()
        assert set(unique_targets).issubset({0.0, 1.0}), f"Target should be binary, got: {unique_targets}"
        
        print("✅ Data quality checks passed!")
        
    def teardown_method(self):
        """Cleanup after tests"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)


if __name__ == "__main__":
    # Run end-to-end tests
    test_suite = TestEndToEnd()
    test_suite.setup_method()
    
    print("🧪 Running end-to-end integration tests...")
    
    try:
        test_suite.test_data_quality_checks()
        print("✅ Data quality test passed")
        
        test_suite.test_complete_pipeline()
        print("✅ Complete pipeline test passed")
        
        print("\n🎉 All end-to-end tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        test_suite.teardown_method()
