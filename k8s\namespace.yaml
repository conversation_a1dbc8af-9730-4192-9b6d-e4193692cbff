apiVersion: v1
kind: Namespace
metadata:
  name: v5trader
  labels:
    name: v5trader
    environment: production
    app: v5trader-platform
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: v5trader-quota
  namespace: v5trader
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "10"
    configmaps: "10"
