import os
import time
from datetime import datetime
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.cuda.amp import autocast, GradScaler
import pytorch_lightning as pl
from torchmetrics import MeanSquaredError, MeanAbsoluteError
import mlflow
import mlflow.pytorch


class ProductionTrainingPipeline:
    """
    Enterprise-Grade Training Pipeline mit MLflow Integration
    """
    
    def __init__(self, mlflow_tracking_uri="http://localhost:5000"):
        mlflow.set_tracking_uri(mlflow_tracking_uri)
        self.experiment_name = "V5_Trader_Advanced_Models"
        
        try:
            mlflow.create_experiment(self.experiment_name)
        except:
            pass  # Experiment existiert bereits
            
        mlflow.set_experiment(self.experiment_name)
    
    def train_ensemble(self, data_module, n_models=5, hyperparameter_ranges=None):
        """
        Trainiert Ensemble von Modellen mit automatischer Hyperparameter-Optimierung
        """
        from optuna.integration import PyTorchLightningPruningCallback
        import optuna
        
        def objective(trial):
            # Hyperparameter Sampling
            params = {
                'd_model': trial.suggest_categorical('d_model', [256, 512, 768]),
                'n_heads': trial.suggest_categorical('n_heads', [8, 12, 16]),
                'n_encoder_layers': trial.suggest_int('n_encoder_layers', 4, 8),
                'dropout': trial.suggest_float('dropout', 0.1, 0.3),
                'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-3, log=True),
                'cnn_filters': trial.suggest_categorical('cnn_filters', [64, 128, 256]),
                'lstm_units': trial.suggest_categorical('lstm_units', [128, 256, 512]),
            }
            
            with mlflow.start_run(nested=True):
                # Log Hyperparameters
                mlflow.log_params(params)
                
                # Model Creation
                model = AdvancedFinancialTransformer(**params)
                
                # Callbacks
                callbacks = [
                    pl.callbacks.EarlyStopping(
                        monitor='val_loss',
                        patience=15,
                        verbose=True,
                        mode='min'
                    ),
                    pl.callbacks.ModelCheckpoint(
                        monitor='val_loss',
                        save_top_k=1,
                        mode='min',
                        save_last=True
                    ),
                    pl.callbacks.LearningRateMonitor(logging_interval='step'),
                    PyTorchLightningPruningCallback(trial, monitor="val_loss")
                ]
                
                # Trainer mit Advanced Settings
                trainer = pl.Trainer(
                    max_epochs=100,
                    accelerator='gpu' if torch.cuda.is_available() else 'cpu',
                    devices=1,
                    precision='16-mixed' if torch.cuda.is_available() else 32,
                    callbacks=callbacks,
                    gradient_clip_val=1.0,
                    accumulate_grad_batches=2,
                    deterministic=True,
                    enable_progress_bar=False
                )
                
                # Training
                trainer.fit(model, data_module)
                
                # Log Final Metrics
                val_loss = trainer.callback_metrics['val_loss'].item()
                mlflow.log_metric("final_val_loss", val_loss)
                
                # Log Model
                mlflow.pytorch.log_model(model, "model")
                
                return val_loss
        
        # Optuna Study
        study = optuna.create_study(
            direction='minimize',
            sampler=optuna.samplers.TPESampler(seed=42),
            pruner=optuna.pruners.MedianPruner(n_startup_trials=5, n_warmup_steps=30)
        )
        
        study.optimize(objective, n_trials=50, timeout=3600*6)  # 6 Stunden
        
        # Beste Parameter für Ensemble Training
        best_params = study.best_params
        
        # Ensemble Training
        ensemble_models = []
        for i in range(n_models):
            with mlflow.start_run(run_name=f"ensemble_model_{i}"):
                # Slight parameter variation für Ensemble Diversity
                varied_params = best_params.copy()
                varied_params['dropout'] *= np.random.uniform(0.8, 1.2)
                varied_params['learning_rate'] *= np.random.uniform(0.8, 1.2)
                
                model = AdvancedFinancialTransformer(**varied_params)
                
                trainer = pl.Trainer(
                    max_epochs=150,
                    accelerator='gpu' if torch.cuda.is_available() else 'cpu',
                    devices=1,
                    precision='16-mixed' if torch.cuda.is_available() else 32,
                    callbacks=[
                        pl.callbacks.EarlyStopping(monitor='val_loss', patience=20),
                        pl.callbacks.ModelCheckpoint(monitor='val_loss', save_top_k=1)
                    ]
                )
                
                trainer.fit(model, data_module)
                ensemble_models.append(model)
                
                # Register in MLflow Model Registry
                model_uri = f"runs:/{mlflow.active_run().info.run_id}/model"
                mlflow.register_model(model_uri, f"V5Trader_Ensemble_Model_{i}")
        
        return ensemble_models, study
    
    def deploy_to_production(self, model_name, stage="Production"):
        """
        Produktionsdeployment über MLflow
        """
        client = mlflow.MlflowClient()
        
        # Hole Latest Model Version
        latest_version = client.get_latest_versions(
            model_name, 
            stages=["None"]
        )[0]
        
        # Transition zu Production
        client.transition_model_version_stage(
            name=model_name,
            version=latest_version.version,
            stage=stage,
            archive_existing_versions=True
        )
        
        print(f"Model {model_name} version {latest_version.version} deployed to {stage}")
        
        return latest_version
