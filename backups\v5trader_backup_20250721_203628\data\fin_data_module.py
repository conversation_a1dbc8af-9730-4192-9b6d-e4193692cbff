# data/fin_data_module.py
import pandas as pd
import torch
from torch.utils.data import DataLoader, Dataset
import pytorch_lightning as pl

class TimeSeriesDataset(Dataset):
    def __init__(self, features: pd.DataFrame, targets: pd.Series, seq_len: int):
        # 1) Ensure features are numeric and handle NaNs
        if features.empty:
            raise ValueError("Features DataFrame is empty!")

        # Convert to numeric if not already (handles object dtypes)
        features_clean = features.copy()
        original_dtypes = features.dtypes.copy()

        for col in features_clean.columns:
            features_clean[col] = pd.to_numeric(features_clean[col], errors='coerce')

        # Check if all values became NaN (indicating non-numeric data)
        all_nan_cols = []
        for col in features_clean.columns:
            if features_clean[col].isna().all():
                all_nan_cols.append(col)

        if len(all_nan_cols) == len(features_clean.columns):
            raise ValueError(f"No numeric features found! Original dtypes: {original_dtypes}")

        # Select only numeric columns and fill NaNs
        features_num = features_clean.select_dtypes(include=["number"]).ffill().fillna(0.0)

        if features_num.empty or features_num.shape[1] == 0:
            raise ValueError(f"No numeric features found! Original dtypes: {original_dtypes}")

        targets_num = targets.ffill().fillna(0.0)

        self.features = torch.tensor(features_num.values, dtype=torch.float32)
        self.targets = torch.tensor(targets_num.values, dtype=torch.float32)
        self.seq_len = seq_len

        print(f"✅ TimeSeriesDataset created:")
        print(f"   Features shape: {self.features.shape}")
        print(f"   Targets shape: {self.targets.shape}")
        print(f"   Sequence length: {self.seq_len}")

    def __len__(self):
        return len(self.features) - self.seq_len

    def __getitem__(self, idx):
        x = self.features[idx : idx + self.seq_len]
        y = self.targets[idx + self.seq_len]
        return x, y

class FinancialDataModule(pl.LightningDataModule):
    def __init__(self, features: pd.DataFrame, targets: pd.Series,
                 seq_len: int = 120, batch_size: int = 64, val_split: float = 0.2):
        super().__init__()
        self.features = features
        self.targets  = targets
        self.seq_len  = seq_len
        self.batch_size = batch_size
        self.val_split  = val_split

    def setup(self, stage=None):
        dataset = TimeSeriesDataset(self.features, self.targets, self.seq_len)
        val_size = int(len(dataset) * self.val_split)
        train_size = len(dataset) - val_size
        self.train_dataset, self.val_dataset = torch.utils.data.random_split(
            dataset, [train_size, val_size]
        )

    def train_dataloader(self):
        return DataLoader(self.train_dataset, batch_size=self.batch_size, shuffle=True)

    def val_dataloader(self):
        return DataLoader(self.val_dataset, batch_size=self.batch_size)
