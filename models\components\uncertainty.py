#!/usr/bin/env python3
"""
Uncertainty estimation components for V5_Trader models
Implements various uncertainty quantification methods
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional
import math

class UncertaintyEstimator(nn.Module):
    """Uncertainty estimation using Monte Carlo Dropout and ensemble methods"""
    
    def __init__(self, input_size: int, hidden_size: int = 128, dropout_rate: float = 0.1):
        super().__init__()
        self.dropout_rate = dropout_rate
        
        self.uncertainty_head = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_size // 2, 2)  # Mean and log variance
        )
    
    def forward(self, x: torch.Tensor, num_samples: int = 10) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:
        """
        Forward pass with uncertainty estimation
        
        Args:
            x: Input tensor
            num_samples: Number of Monte Carlo samples
            
        Returns:
            Tuple of (mean_prediction, uncertainty)
        """
        if self.training:
            # During training, return single forward pass
            output = self.uncertainty_head(x)
            mean = output[:, 0:1]
            log_var = output[:, 1:2]
            uncertainty = torch.exp(0.5 * log_var)
            return mean, uncertainty
        else:
            # During inference, use Monte Carlo sampling
            predictions = []
            
            # Enable dropout for uncertainty estimation
            self.train()
            
            with torch.no_grad():
                for _ in range(num_samples):
                    output = self.uncertainty_head(x)
                    predictions.append(output[:, 0:1])
            
            # Disable dropout
            self.eval()
            
            # Calculate statistics
            predictions = torch.stack(predictions, dim=0)
            mean_prediction = torch.mean(predictions, dim=0)
            uncertainty = torch.std(predictions, dim=0)
            
            return mean_prediction, uncertainty

class BayesianLinear(nn.Module):
    """Bayesian linear layer with weight uncertainty"""
    
    def __init__(self, in_features: int, out_features: int, prior_std: float = 1.0):
        super().__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.prior_std = prior_std
        
        # Weight parameters
        self.weight_mu = nn.Parameter(torch.randn(out_features, in_features) * 0.1)
        self.weight_rho = nn.Parameter(torch.randn(out_features, in_features) * 0.1)
        
        # Bias parameters
        self.bias_mu = nn.Parameter(torch.randn(out_features) * 0.1)
        self.bias_rho = nn.Parameter(torch.randn(out_features) * 0.1)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # Sample weights and biases
        weight_std = torch.log1p(torch.exp(self.weight_rho))
        weight = self.weight_mu + weight_std * torch.randn_like(weight_std)
        
        bias_std = torch.log1p(torch.exp(self.bias_rho))
        bias = self.bias_mu + bias_std * torch.randn_like(bias_std)
        
        return F.linear(x, weight, bias)
    
    def kl_divergence(self) -> torch.Tensor:
        """Calculate KL divergence for regularization"""
        weight_std = torch.log1p(torch.exp(self.weight_rho))
        bias_std = torch.log1p(torch.exp(self.bias_rho))
        
        # KL divergence for weights
        weight_kl = 0.5 * torch.sum(
            (self.weight_mu ** 2 + weight_std ** 2) / (self.prior_std ** 2) - 
            torch.log(weight_std ** 2) + torch.log(self.prior_std ** 2) - 1
        )
        
        # KL divergence for biases
        bias_kl = 0.5 * torch.sum(
            (self.bias_mu ** 2 + bias_std ** 2) / (self.prior_std ** 2) - 
            torch.log(bias_std ** 2) + torch.log(self.prior_std ** 2) - 1
        )
        
        return weight_kl + bias_kl

class EnsembleHead(nn.Module):
    """Ensemble prediction head for uncertainty estimation"""
    
    def __init__(self, input_size: int, output_size: int, num_heads: int = 5):
        super().__init__()
        self.num_heads = num_heads
        
        self.heads = nn.ModuleList([
            nn.Sequential(
                nn.Linear(input_size, input_size // 2),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(input_size // 2, output_size)
            ) for _ in range(num_heads)
        ])
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass with ensemble prediction
        
        Returns:
            Tuple of (mean_prediction, uncertainty)
        """
        predictions = torch.stack([head(x) for head in self.heads], dim=0)
        
        mean_prediction = torch.mean(predictions, dim=0)
        uncertainty = torch.std(predictions, dim=0)
        
        return mean_prediction, uncertainty

class VariationalDropout(nn.Module):
    """Variational dropout for uncertainty estimation"""
    
    def __init__(self, input_size: int, dropout_rate: float = 0.1):
        super().__init__()
        self.input_size = input_size
        self.dropout_rate = dropout_rate
        
        # Learnable dropout parameters
        self.log_alpha = nn.Parameter(torch.randn(input_size) * 0.1)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        if not self.training:
            return x
        
        # Calculate dropout probabilities
        alpha = torch.exp(self.log_alpha)
        epsilon = torch.randn_like(x)
        
        # Apply variational dropout
        return x * (1 + alpha * epsilon)
    
    def kl_divergence(self) -> torch.Tensor:
        """Calculate KL divergence for regularization"""
        alpha = torch.exp(self.log_alpha)
        k1, k2, k3 = 0.63576, 1.8732, 1.48695
        
        kl = k1 * torch.sigmoid(k2 + k3 * self.log_alpha) - 0.5 * torch.log1p(1 / alpha) - k1
        
        return torch.sum(kl)

class DeepEnsemble(nn.Module):
    """Deep ensemble for uncertainty estimation"""
    
    def __init__(self, model_class, model_args: dict, num_models: int = 5):
        super().__init__()
        self.num_models = num_models
        
        self.models = nn.ModuleList([
            model_class(**model_args) for _ in range(num_models)
        ])
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass with ensemble prediction
        
        Returns:
            Tuple of (mean_prediction, uncertainty)
        """
        predictions = torch.stack([model(x) for model in self.models], dim=0)
        
        mean_prediction = torch.mean(predictions, dim=0)
        uncertainty = torch.std(predictions, dim=0)
        
        return mean_prediction, uncertainty
    
    def train_model(self, model_idx: int, mode: bool = True):
        """Set training mode for specific model"""
        self.models[model_idx].train(mode)

class ConfidenceHead(nn.Module):
    """Confidence estimation head"""
    
    def __init__(self, input_size: int, hidden_size: int = 64):
        super().__init__()
        self.confidence_head = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_size, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Return confidence score between 0 and 1"""
        return self.confidence_head(x)

class UncertaintyLoss(nn.Module):
    """Loss function that incorporates uncertainty"""
    
    def __init__(self, base_loss_fn=nn.MSELoss()):
        super().__init__()
        self.base_loss_fn = base_loss_fn
    
    def forward(self, predictions: torch.Tensor, targets: torch.Tensor, 
                uncertainty: torch.Tensor) -> torch.Tensor:
        """
        Calculate uncertainty-aware loss
        
        Args:
            predictions: Model predictions
            targets: Ground truth targets
            uncertainty: Predicted uncertainty
            
        Returns:
            Combined loss
        """
        # Base prediction loss
        base_loss = self.base_loss_fn(predictions, targets)
        
        # Uncertainty regularization
        # Higher uncertainty should be penalized when predictions are wrong
        prediction_error = torch.abs(predictions - targets)
        uncertainty_loss = torch.mean(prediction_error / (uncertainty + 1e-8) + torch.log(uncertainty + 1e-8))
        
        return base_loss + 0.1 * uncertainty_loss
