import asyncio
import websockets
import json
import random
import pandas as pd
import os
import sys
from datetime import datetime
from typing import Set

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class MarketDataWebSocket:
    """WebSocket Server für Real-Time Market Data"""
    
    def __init__(self):
        self.clients: Set = set()
        self.running = True
        self.market_data = {}
        
    async def register_client(self, websocket):
        """Registriert einen neuen Client"""
        self.clients.add(websocket)
        print(f"Client verbunden. Aktuelle Clients: {len(self.clients)}")
        
        # Sende aktuelle Marktdaten an neuen Client
        if self.market_data:
            welcome_message = {
                "type": "welcome",
                "message": "Connected to V5 Trader WebSocket",
                "current_data": self.market_data
            }
            await websocket.send(json.dumps(welcome_message))
        
    async def unregister_client(self, websocket):
        """Entfernt einen Client"""
        self.clients.discard(websocket)
        print(f"Client getrennt. Aktuelle Clients: {len(self.clients)}")
        
    async def broadcast_market_data(self, data):
        """Sendet Daten an alle verbundenen Clients"""
        if self.clients:
            message = json.dumps(data)
            disconnected_clients = []
            
            for client in self.clients.copy():
                try:
                    await client.send(message)
                except websockets.exceptions.ConnectionClosed:
                    disconnected_clients.append(client)
            
            # Entferne getrennte Clients
            for client in disconnected_clients:
                self.clients.discard(client)
    
    async def handle_client(self, websocket):
        """Behandelt eine Client-Verbindung"""
        await self.register_client(websocket)
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.handle_client_message(websocket, data)
                except json.JSONDecodeError:
                    # Echo zurück senden für einfache Nachrichten
                    await websocket.send(f"Echo: {message}")
        except websockets.exceptions.ConnectionClosed:
            pass
        finally:
            await self.unregister_client(websocket)
    
    async def handle_client_message(self, websocket, data):
        """Behandelt Nachrichten von Clients"""
        message_type = data.get("type", "unknown")
        
        if message_type == "subscribe":
            symbols = data.get("symbols", [])
            response = {
                "type": "subscription_confirmed",
                "symbols": symbols,
                "message": f"Subscribed to {len(symbols)} symbols"
            }
            await websocket.send(json.dumps(response))
            
        elif message_type == "get_status":
            status = {
                "type": "status",
                "connected_clients": len(self.clients),
                "active_symbols": list(self.market_data.keys()),
                "server_time": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(status))
    
    def load_real_market_data(self):
        """Lädt echte Marktdaten falls verfügbar"""
        try:
            if os.path.exists("data/market_data.csv"):
                df = pd.read_csv("data/market_data.csv")
                # Nehme die neuesten Daten für jedes Symbol
                latest_data = df.groupby('Ticker').tail(1)
                
                for _, row in latest_data.iterrows():
                    self.market_data[row['Ticker']] = {
                        "price": float(row['Close']),
                        "open": float(row['Open']),
                        "high": float(row['High']),
                        "low": float(row['Low']),
                        "volume": int(row['Volume'])
                    }
                return True
        except Exception as e:
            print(f"Fehler beim Laden der Marktdaten: {e}")
        return False
    
    async def generate_mock_data(self):
        """Generiert Mock-Marktdaten"""
        # Versuche zuerst echte Daten zu laden
        has_real_data = self.load_real_market_data()
        
        if not has_real_data:
            # Fallback auf Mock-Daten
            symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN"]
            base_prices = {"AAPL": 150, "GOOGL": 2800, "MSFT": 300, "TSLA": 800, "AMZN": 3300}
            
            for symbol in symbols:
                self.market_data[symbol] = {
                    "price": base_prices[symbol],
                    "open": base_prices[symbol],
                    "high": base_prices[symbol] * 1.02,
                    "low": base_prices[symbol] * 0.98,
                    "volume": random.randint(1000000, 5000000)
                }
        
        while self.running:
            for symbol in self.market_data.keys():
                # Simuliere Preisbewegung
                current_data = self.market_data[symbol]
                price_change = random.uniform(-0.01, 0.01)  # ±1%
                new_price = current_data["price"] * (1 + price_change)
                
                # Update Marktdaten
                self.market_data[symbol]["price"] = round(new_price, 2)
                self.market_data[symbol]["high"] = max(current_data["high"], new_price)
                self.market_data[symbol]["low"] = min(current_data["low"], new_price)
                
                # Broadcast Update
                data = {
                    "type": "market_update",
                    "symbol": symbol,
                    "price": round(new_price, 2),
                    "change": round(price_change * 100, 2),
                    "change_percent": round(price_change * 100, 2),
                    "timestamp": datetime.now().isoformat(),
                    "volume": current_data["volume"],
                    "high": self.market_data[symbol]["high"],
                    "low": self.market_data[symbol]["low"],
                    "open": current_data["open"]
                }
                
                await self.broadcast_market_data(data)
                
            await asyncio.sleep(2)  # Update alle 2 Sekunden
    
    async def start_server(self, host="localhost", port=8765):
        """Startet den WebSocket Server"""
        print(f"🚀 WebSocket Server startet auf ws://{host}:{port}")
        
        # Starte Mock-Daten Generator
        asyncio.create_task(self.generate_mock_data())
        
        # Starte WebSocket Server
        server = await websockets.serve(self.handle_client, host, port)
        print("✅ WebSocket Server läuft!")
        print("📡 Verfügbare Nachrichten:")
        print("   - {\"type\": \"subscribe\", \"symbols\": [\"AAPL\", \"MSFT\"]}")
        print("   - {\"type\": \"get_status\"}")
        
        # Halte Server am Laufen
        try:
            await server.wait_closed()
        except KeyboardInterrupt:
            print("\n🛑 WebSocket Server wird beendet...")
            self.running = False


if __name__ == "__main__":
    server = MarketDataWebSocket()
    try:
        asyncio.run(server.start_server())
    except KeyboardInterrupt:
        print("\n🛑 Server beendet!")
