from fin_data_module import FinancialDataModule
import pandas as pd
import torch

def main():
    # 1) Synthetische Beispieldaten erzeugen
    N = 1000
    # 10 Features, 1000 Zeitschritte
    features = pd.DataFrame(torch.rand(N, 10).numpy(), columns=[f"f{i}" for i in range(10)])
    targets  = pd.Series(torch.rand(N).numpy())

    # 2) DataModule initialisieren
    dm = FinancialDataModule(features, targets, batch_size=32, val_split=0.2)
    dm.setup()  # Teilt in Trainings- und Validierungsdatensatz auf

    # 3) Erster Batch ausgeben
    train_loader = dm.train_dataloader()
    batch_X, batch_y = next(iter(train_loader))
    print("Train-Batch X-Shape:", batch_X.shape, "y-Shape:", batch_y.shape)

    val_loader = dm.val_dataloader()
    batch_Xv, batch_yv = next(iter(val_loader))
    print("Val-Batch   X-Shape:", batch_Xv.shape, "y-Shape:", batch_yv.shape)

if __name__ == "__main__":
    main()
