# Integration Test Suite für V5_Trader System
# Diese Datei implementiert umfassende End-to-End Tests

import asyncio
import pytest
import requests
import redis
import psycopg2
import time
import json
import websockets
from datetime import datetime, timedelta
from typing import Dict, List
import pandas as pd

class V5TraderIntegrationTests:
    """Umfassende Integration Tests für das V5 Trader System"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.mlflow_url = "http://localhost:5000"
        self.websocket_url = "ws://localhost:8765"
        self.test_results = []
        
    def log_test_result(self, test_name: str, status: str, details: str = ""):
        """Loggt Testergebnisse"""
        self.test_results.append({
            "test": test_name,
            "status": status,
            "details": details,
            "timestamp": datetime.now().isoformat()
        })
        
    def test_api_gateway_health(self) -> bool:
        """Test 1: API Gateway Gesundheitscheck"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "healthy":
                    self.log_test_result("API Gateway Health", "PASS", f"Response: {data}")
                    return True
                    
            self.log_test_result("API Gateway Health", "FAIL", f"Status: {response.status_code}")
            return False
            
        except Exception as e:
            self.log_test_result("API Gateway Health", "ERROR", str(e))
            return False
    
    def test_market_data_endpoint(self) -> bool:
        """Test 2: Market Data API Endpoint"""
        try:
            response = requests.get(f"{self.base_url}/api/market-data?symbol=AAPL", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                required_fields = ["symbol", "price", "change", "change_percent", "timestamp"]
                
                if all(field in data for field in required_fields):
                    self.log_test_result("Market Data API", "PASS", f"Data: {data}")
                    return True
                    
            self.log_test_result("Market Data API", "FAIL", f"Missing fields or status {response.status_code}")
            return False
            
        except Exception as e:
            self.log_test_result("Market Data API", "ERROR", str(e))
            return False
    
    def test_database_connectivity(self) -> bool:
        """Test 3: PostgreSQL Datenbankverbindung"""
        try:
            conn = psycopg2.connect(
                host="localhost",
                database="v5trader",
                user="postgres",
                password="your_password",  # Aus .env laden
                connect_timeout=10
            )
            
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()
            
            cursor.close()
            conn.close()
            
            self.log_test_result("Database Connectivity", "PASS", f"PostgreSQL: {version[0]}")
            return True
            
        except Exception as e:
            self.log_test_result("Database Connectivity", "ERROR", str(e))
            return False
    
    def test_redis_connectivity(self) -> bool:
        """Test 4: Redis Cache Verbindung"""
        try:
            r = redis.Redis(host='localhost', port=6379, decode_responses=True, socket_timeout=10)
            
            # Test Set/Get
            test_key = f"test_key_{int(time.time())}"
            test_value = "v5trader_test_value"
            
            r.set(test_key, test_value, ex=60)  # 60 Sekunden TTL
            retrieved_value = r.get(test_key)
            
            if retrieved_value == test_value:
                r.delete(test_key)
                self.log_test_result("Redis Connectivity", "PASS", f"Set/Get successful")
                return True
            else:
                self.log_test_result("Redis Connectivity", "FAIL", f"Value mismatch: {retrieved_value}")
                return False
                
        except Exception as e:
            self.log_test_result("Redis Connectivity", "ERROR", str(e))
            return False
    
    def test_mlflow_service(self) -> bool:
        """Test 5: MLflow Tracking Server"""
        try:
            response = requests.get(f"{self.mlflow_url}/health", timeout=10)
            
            if response.status_code == 200:
                # Test Experiments API
                exp_response = requests.get(f"{self.mlflow_url}/api/2.0/mlflow/experiments/list", timeout=10)
                
                if exp_response.status_code == 200:
                    self.log_test_result("MLflow Service", "PASS", "Experiments API accessible")
                    return True
                    
            self.log_test_result("MLflow Service", "FAIL", f"Status: {response.status_code}")
            return False
            
        except Exception as e:
            self.log_test_result("MLflow Service", "ERROR", str(e))
            return False
    
    async def test_websocket_connection(self) -> bool:
        """Test 6: WebSocket Real-Time Verbindung"""
        try:
            async with websockets.connect(self.websocket_url, timeout=10) as websocket:
                # Test Echo
                test_message = "v5trader_websocket_test"
                await websocket.send(test_message)
                
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                
                if test_message in response:
                    self.log_test_result("WebSocket Connection", "PASS", f"Echo successful: {response}")
                    return True
                else:
                    self.log_test_result("WebSocket Connection", "FAIL", f"Echo failed: {response}")
                    return False
                    
        except Exception as e:
            self.log_test_result("WebSocket Connection", "ERROR", str(e))
            return False
    
    async def test_websocket_market_data(self) -> bool:
        """Test 7: WebSocket Market Data Stream"""
        try:
            async with websockets.connect(self.websocket_url, timeout=10) as websocket:
                # Warte auf Market Data
                message = await asyncio.wait_for(websocket.recv(), timeout=15)
                data = json.loads(message)
                
                required_fields = ["type", "symbol", "price", "change", "timestamp"]
                
                if data.get("type") == "market_update" and all(field in data for field in required_fields):
                    self.log_test_result("WebSocket Market Data", "PASS", f"Received: {data}")
                    return True
                else:
                    self.log_test_result("WebSocket Market Data", "FAIL", f"Invalid data: {data}")
                    return False
                    
        except Exception as e:
            self.log_test_result("WebSocket Market Data", "ERROR", str(e))
            return False
    
    def test_docker_services(self) -> bool:
        """Test 8: Docker Services Status"""
        try:
            import subprocess
            
            result = subprocess.run(['docker-compose', 'ps'], 
                                  capture_output=True, text=True, 
                                  cwd='d:/V5_Trader', timeout=30)
            
            if result.returncode == 0:
                output = result.stdout
                services = ["postgres", "redis", "mlflow", "api-gateway"]
                
                running_services = []
                for service in services:
                    if service in output and "Up" in output:
                        running_services.append(service)
                
                if len(running_services) >= 3:  # Mindestens 3 Services müssen laufen
                    self.log_test_result("Docker Services", "PASS", f"Running: {running_services}")
                    return True
                else:
                    self.log_test_result("Docker Services", "FAIL", f"Only running: {running_services}")
                    return False
            else:
                self.log_test_result("Docker Services", "ERROR", f"Docker compose error: {result.stderr}")
                return False
                
        except Exception as e:
            self.log_test_result("Docker Services", "ERROR", str(e))
            return False
    
    def test_system_performance(self) -> bool:
        """Test 9: System Performance Benchmarks"""
        try:
            start_time = time.time()
            
            # API Latenz Test
            response = requests.get(f"{self.base_url}/api/market-data?symbol=MSFT", timeout=5)
            api_latency = (time.time() - start_time) * 1000  # ms
            
            # Redis Latenz Test
            redis_start = time.time()
            r = redis.Redis(host='localhost', port=6379, decode_responses=True)
            r.ping()
            redis_latency = (time.time() - redis_start) * 1000  # ms
            
            performance_ok = api_latency < 1000 and redis_latency < 50  # 1s API, 50ms Redis
            
            if performance_ok:
                self.log_test_result("System Performance", "PASS", 
                                   f"API: {api_latency:.2f}ms, Redis: {redis_latency:.2f}ms")
                return True
            else:
                self.log_test_result("System Performance", "FAIL", 
                                   f"API: {api_latency:.2f}ms, Redis: {redis_latency:.2f}ms")
                return False
                
        except Exception as e:
            self.log_test_result("System Performance", "ERROR", str(e))
            return False
    
    def test_data_pipeline(self) -> bool:
        """Test 10: Daten Pipeline Integration"""
        try:
            # Test ob Marktdaten verarbeitet werden können
            test_data = {
                "symbol": "TEST",
                "timestamp": datetime.now().isoformat(),
                "open": 100.0,
                "high": 105.0,
                "low": 99.0,
                "close": 102.0,
                "volume": 1000000
            }
            
            # Simuliere Datenverarbeitung
            df = pd.DataFrame([test_data])
            
            # Basis-Validierung
            if len(df) == 1 and df['close'].iloc[0] == 102.0:
                self.log_test_result("Data Pipeline", "PASS", f"Data processing successful")
                return True
            else:
                self.log_test_result("Data Pipeline", "FAIL", f"Data validation failed")
                return False
                
        except Exception as e:
            self.log_test_result("Data Pipeline", "ERROR", str(e))
            return False
    
    async def run_all_tests(self) -> Dict:
        """Führt alle Tests aus und gibt Ergebnisse zurück"""
        print("🚀 Starte V5 Trader Integration Tests...")
        print("=" * 60)
        
        tests = [
            ("API Gateway Health", self.test_api_gateway_health),
            ("Market Data API", self.test_market_data_endpoint),
            ("Database Connectivity", self.test_database_connectivity),
            ("Redis Connectivity", self.test_redis_connectivity),
            ("MLflow Service", self.test_mlflow_service),
            ("WebSocket Connection", self.test_websocket_connection),
            ("WebSocket Market Data", self.test_websocket_market_data),
            ("Docker Services", self.test_docker_services),
            ("System Performance", self.test_system_performance),
            ("Data Pipeline", self.test_data_pipeline)
        ]
        
        passed = 0
        failed = 0
        errors = 0
        
        for test_name, test_func in tests:
            print(f"🔍 Teste {test_name}...")
            
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                if result:
                    print(f"  ✅ {test_name} - BESTANDEN")
                    passed += 1
                else:
                    print(f"  ❌ {test_name} - FEHLGESCHLAGEN")
                    failed += 1
                    
            except Exception as e:
                print(f"  💥 {test_name} - FEHLER: {str(e)}")
                errors += 1
            
            time.sleep(1)  # Pause zwischen Tests
        
        # Zusammenfassung
        total = passed + failed + errors
        success_rate = (passed / total * 100) if total > 0 else 0
        
        summary = {
            "total_tests": total,
            "passed": passed,
            "failed": failed,
            "errors": errors,
            "success_rate": success_rate,
            "timestamp": datetime.now().isoformat(),
            "test_results": self.test_results
        }
        
        print("\n" + "=" * 60)
        print("📊 TEST ZUSAMMENFASSUNG")
        print("=" * 60)
        print(f"Gesamt Tests: {total}")
        print(f"✅ Bestanden: {passed}")
        print(f"❌ Fehlgeschlagen: {failed}")
        print(f"💥 Fehler: {errors}")
        print(f"📈 Erfolgsrate: {success_rate:.1f}%")
        print("=" * 60)
        
        # Detaillierte Ergebnisse
        print("\n🔍 DETAILLIERTE ERGEBNISSE:")
        for result in self.test_results:
            status_icon = {"PASS": "✅", "FAIL": "❌", "ERROR": "💥"}.get(result["status"], "❓")
            print(f"{status_icon} {result['test']}: {result['status']}")
            if result["details"]:
                print(f"   Details: {result['details']}")
        
        return summary
    
    def save_test_report(self, summary: Dict):
        """Speichert Testbericht als JSON"""
        import json
        
        report_file = f"d:/V5_Trader/test_reports/integration_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            import os
            os.makedirs("d:/V5_Trader/test_reports", exist_ok=True)
            
            with open(report_file, 'w') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Testbericht gespeichert: {report_file}")
            
        except Exception as e:
            print(f"❌ Fehler beim Speichern des Berichts: {e}")

async def main():
    """Hauptfunktion für Test-Ausführung"""
    tester = V5TraderIntegrationTests()
    
    # Warte auf System-Bereitschaft
    print("⏳ Warte auf System-Bereitschaft...")
    max_retries = 30
    
    for i in range(max_retries):
        try:
            response = requests.get("http://localhost:8000/health", timeout=2)
            if response.status_code == 200:
                print("✅ System ist bereit!")
                break
        except:
            if i < max_retries - 1:
                print(f"   Warte... ({i+1}/{max_retries})")
                time.sleep(2)
            else:
                print("❌ System nicht bereit - führe Tests trotzdem aus")
    
    # Tests ausführen
    summary = await tester.run_all_tests()
    
    # Bericht speichern
    tester.save_test_report(summary)
    
    # Exit Code basierend auf Testergebnissen
    if summary["failed"] > 0 or summary["errors"] > 0:
        print("\n❌ Einige Tests sind fehlgeschlagen!")
        return 1
    else:
        print("\n🎉 Alle Tests erfolgreich!")
        return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)