# 📦 V5_Trader Installation Guide

## 🎯 Quick Start

### Prerequisites
- **Python 3.13+** (recommended)
- **<PERSON>er & Docker Compose**
- **Git**
- **8GB+ RAM** (for ML models)

### 1. <PERSON><PERSON> and Setup
```bash
git clone <repository-url>
cd V5_Trader
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

Required environment variables:
```bash
# Database
POSTGRES_PASSWORD=secure_password_2024
POSTGRES_DB=v5trader
POSTGRES_USER=postgres

# MLflow
MLFLOW_TRACKING_URI=http://localhost:5000
MLFLOW_EXPERIMENT_NAME=V5Trader_Production

# API Keys (optional)
ALPHA_VANTAGE_API_KEY=your_key_here
POLYGON_API_KEY=your_key_here
```

### 4. Start Infrastructure
```bash
# Start database and monitoring
docker-compose up -d postgres redis mlflow prometheus grafana

# Verify services
docker-compose ps
```

### 5. Initialize Data
```bash
# Fetch market data
python fetch_market_data.py

# Verify data
python check_data_size.py
```

### 6. Start Application
```bash
# Option A: Full system launcher
python launcher/system_launcher.py start

# Option B: Manual startup
python start_v5trader.py
```

### 7. Verify Installation
```bash
# Run health checks
python launcher/system_launcher.py check

# Run integration tests
python tests/test_integration_consolidated.py
```

## 🌐 Access Points

After successful installation:

- **Web Dashboard**: http://localhost:8000/dashboard/dashboard.html
- **API Documentation**: http://localhost:8000/docs
- **MLflow UI**: http://localhost:5000
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090

## 🔧 Development Setup

### IDE Configuration
```bash
# VS Code settings
mkdir .vscode
cat > .vscode/settings.json << EOF
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.formatting.provider": "black"
}
EOF
```

### Pre-commit Hooks
```bash
pip install pre-commit
pre-commit install
```

### Testing Setup
```bash
# Install test dependencies
pip install pytest pytest-cov pytest-asyncio

# Run test suite
pytest tests/ -v --cov=.
```

## 🐳 Docker Development

### Build Custom Images
```bash
# Build API Gateway
docker build -t v5trader/api-gateway services/api_gateway/

# Build complete stack
docker-compose build
```

### Development with Hot Reload
```bash
# Start with volume mounts for development
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```

## 🚨 Troubleshooting

### Common Issues

**Port Conflicts**
```bash
# Check port usage
netstat -tulpn | grep :8000
netstat -tulpn | grep :5000

# Kill conflicting processes
sudo kill -9 $(lsof -t -i:8000)
```

**Database Connection Issues**
```bash
# Reset database
docker-compose down -v
docker-compose up -d postgres
python infrastructure/postgres/init.sql
```

**Memory Issues**
```bash
# Check system resources
python tools/system_monitor.py

# Reduce model size in config/model_config.yaml
# Set smaller batch_size and d_model values
```

**Permission Issues (Linux)**
```bash
# Fix Docker permissions
sudo usermod -aG docker $USER
newgrp docker
```

### Log Analysis
```bash
# View service logs
docker-compose logs -f api-gateway
docker-compose logs -f websocket-server

# Application logs
tail -f logs/application.log
tail -f logs/training.log
```

### Performance Optimization
```bash
# Enable GPU support (if available)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Optimize for production
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
export OMP_NUM_THREADS=4
```

## 📊 Verification Checklist

- [ ] All services start without errors
- [ ] Web dashboard loads and shows live data
- [ ] API endpoints respond correctly
- [ ] Database connections work
- [ ] MLflow tracking is active
- [ ] Prometheus metrics are collected
- [ ] WebSocket connections establish
- [ ] Model training completes successfully
- [ ] Integration tests pass

## 🔄 Updates and Maintenance

### Regular Updates
```bash
# Update dependencies
pip install -r requirements.txt --upgrade

# Update Docker images
docker-compose pull
docker-compose up -d
```

### Backup Procedures
```bash
# Backup database
python tools/maintenance.py backup

# Backup MLflow artifacts
python tools/maintenance.py backup-mlflow
```

### Health Monitoring
```bash
# Automated health checks
python tools/system_monitor.py --continuous

# Manual system check
python launcher/system_launcher.py status
```

## 🚀 Production Deployment

### Kubernetes Deployment
```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secrets.yaml
kubectl apply -f k8s/deployments.yaml
kubectl apply -f k8s/services.yaml

# Verify deployment
kubectl get pods -n v5trader
kubectl get services -n v5trader
```

### Helm Deployment (Recommended)
```bash
# Install Helm chart
helm install v5trader ./helm/v5trader \
  --namespace v5trader \
  --create-namespace \
  --values helm/v5trader/values.prod.yaml

# Upgrade deployment
helm upgrade v5trader ./helm/v5trader \
  --namespace v5trader \
  --values helm/v5trader/values.prod.yaml
```

### CI/CD Pipeline
The project includes a comprehensive GitHub Actions pipeline:
- **Code Quality**: Linting, formatting, type checking
- **Testing**: Unit tests, integration tests, performance tests
- **Security**: Vulnerability scanning with Trivy
- **Build**: Docker image building and pushing
- **Deploy**: Automated deployment to staging/production

---

**Next Steps**: See [02_System_Architecture.md](02_System_Architecture.md) for detailed system overview.
