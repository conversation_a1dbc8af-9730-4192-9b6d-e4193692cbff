from data.fin_data_module import FinancialDataModule
import pandas as pd, os

# CSV laden
df_raw = pd.read_csv(os.path.join("data","market_data.csv"), parse_dates=["Date"])
df_aapl = df_raw[df_raw["Ticker"]=="AAPL"].set_index("Date")
feature_cols = ["Open","High","Low","Close","Volume"]
df_feats = df_aapl[feature_cols]
targets  = df_aapl["Target"]

dm = FinancialDataModule(df_feats, targets, seq_len=120, batch_size=32)
dm.setup()
for x,y in dm.train_dataloader():
    print("batch x shape:", x.shape, "y shape:", y.shape)
    break
