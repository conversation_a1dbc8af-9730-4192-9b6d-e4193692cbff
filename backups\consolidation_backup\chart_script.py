import plotly.graph_objects as go
import plotly.express as px
import json

# Data for the system architecture
data = {
    "layers": [
        {"name": "Data Layer", "components": ["Alpha Vantage API", "Yahoo Finance", "MT5 Connector", "PostgreSQL", "Redis Cache", "MinIO Storage"]},
        {"name": "ML Layer", "components": ["MLflow", "PyTorch", "Feature Store", "Model Registry", "Inference Engine", "AutoML"]},  
        {"name": "Trading Layer", "components": ["Strategy Engine", "Order Manager", "Risk Controller", "Portfolio Manager", "Backtester", "Smart Router"]},
        {"name": "Infrastructure Layer", "components": ["Kubernetes", "Docker", "Prometheus", "Grafana", "ELK Stack", "GitHub Actions"]},
        {"name": "Interface Layer", "components": ["React Dashboard", "FastAPI", "Websockets", "Slack Alerts", "Mobile App", "REST API"]}
    ]
}

# Prepare data for treemap
labels = []
parents = []
values = []
colors = []

# Brand colors
brand_colors = ['#1FB8CD', '#FFC185', '#ECEBD5', '#5D878F', '#D2BA4C']

# Add root
labels.append("AI Trading Platform")
parents.append("")
values.append(30)
colors.append('#13343B')

# Add layers and components
for i, layer in enumerate(data["layers"]):
    layer_name = layer["name"]
    # Add layer
    labels.append(layer_name)
    parents.append("AI Trading Platform")
    values.append(len(layer["components"]))
    colors.append(brand_colors[i % len(brand_colors)])
    
    # Add components
    for component in layer["components"]:
        # Truncate component names to 15 characters as per strict instructions
        comp_name = component if len(component) <= 15 else component[:12] + "..."
        labels.append(comp_name)
        parents.append(layer_name)
        values.append(1)
        colors.append(brand_colors[i % len(brand_colors)])

# Create treemap
fig = go.Figure(go.Treemap(
    labels=labels,
    parents=parents,
    values=values,
    branchvalues="total",
    marker_colors=colors,
    textinfo="label",
    textfont_size=12,
    hovertemplate='<b>%{label}</b><br>Layer: %{parent}<extra></extra>',
    maxdepth=3
))

fig.update_layout(
    title="AI Trading Platform Architecture",
    font=dict(family="Arial", size=10),
)

# Save the chart
fig.write_image("ai_trading_architecture.png")