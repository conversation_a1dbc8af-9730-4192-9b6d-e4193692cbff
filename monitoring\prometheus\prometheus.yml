global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'v5trader-monitor'

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # V5_Trader API Gateway
  - job_name: 'v5trader-api-gateway'
    static_configs:
      - targets: ['api-gateway:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s
    honor_labels: true

  # MLflow Tracking Server
  - job_name: 'v5trader-mlflow'
    static_configs:
      - targets: ['mlflow:5000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # PostgreSQL Database Metrics
  - job_name: 'v5trader-postgres'
    static_configs:
      - targets: ['postgres_exporter:9187']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # Redis Cache Metrics
  - job_name: 'v5trader-redis'
    static_configs:
      - targets: ['redis_exporter:9121']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # System Metrics (Node Exporter)
  - job_name: 'v5trader-system'
    static_configs:
      - targets: ['node_exporter:9100']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # Prometheus Self-Monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Grafana Metrics
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s
