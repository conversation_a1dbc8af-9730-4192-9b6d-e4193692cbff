import pytest
import requests
import time
import pandas as pd
import os
import sys
from datetime import datetime, timedelta

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

class TestSystemIntegration:
    """Integration Tests für das gesamte V5 Trader System"""
    
    BASE_URL = "http://localhost:8000"
    
    def test_api_gateway_health(self):
        """Test ob API Gateway erreichbar ist"""
        try:
            response = requests.get(f"{self.BASE_URL}/health", timeout=5)
            assert response.status_code == 200
            assert response.json()["status"] == "healthy"
        except requests.exceptions.ConnectionError:
            print("⚠️ API Gateway not running")
            return False
    
    def test_market_data_endpoint(self):
        """Test Market Data API Endpoint"""
        try:
            response = requests.get(f"{self.BASE_URL}/api/market-data?symbol=AAPL", timeout=5)
            assert response.status_code == 200
            
            data = response.json()
            assert "symbol" in data
            assert "price" in data
            assert data["symbol"] == "AAPL"
        except requests.exceptions.ConnectionError:
            print("⚠️ API Gateway not running")
            return False
    
    def test_database_connection(self):
        """Test Datenbankverbindung"""
        try:
            import psycopg2
            conn = psycopg2.connect(
                host="localhost",
                database="v5trader",
                user="postgres",
                password="postgres"
            )
            conn.close()
            assert True
        except ImportError:
            print("⚠️ psycopg2 not installed")
            return False
        except Exception:
            print("⚠️ Database not available")
            return False
    
    def test_redis_connection(self):
        """Test Redis Verbindung"""
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, decode_responses=True)
            
            # Test Set/Get
            test_key = "test_key"
            test_value = "test_value"
            
            r.set(test_key, test_value)
            retrieved_value = r.get(test_key)
            
            assert retrieved_value == test_value
            r.delete(test_key)
        except ImportError:
            print("⚠️ redis not installed")
            return False
        except Exception:
            print("⚠️ Redis not available")
            return False

    def test_strategy_execution(self):
        """Test Trading Strategy Execution"""
        from strategies.technical_indicators import SimpleMovingAverageStrategy
        
        # Create sample data
        dates = pd.date_range('2023-01-01', periods=50, freq='D')
        sample_data = pd.DataFrame({
            'Date': dates,
            'Open': 100 + pd.Series(range(50)) * 0.5,
            'High': 105 + pd.Series(range(50)) * 0.5,
            'Low': 95 + pd.Series(range(50)) * 0.5,
            'Close': 100 + pd.Series(range(50)) * 0.5,
            'Volume': 1000000
        })
        
        # Test strategy
        strategy_params = {"short_window": 5, "long_window": 10}
        strategy = SimpleMovingAverageStrategy(strategy_params)
        
        signal = strategy.generate_signals(sample_data)
        assert "signal" in signal
        assert "confidence" in signal
        assert signal["signal"] in ["BUY", "SELL", "HOLD"]


def run_system_tests():
    """Führt alle System-Tests aus"""
    print("🧪 Starte System-Tests...")
    
    # Warte bis Services bereit sind
    max_retries = 10
    for i in range(max_retries):
        try:
            response = requests.get("http://localhost:8000/health", timeout=2)
            if response.status_code == 200:
                print("✅ API Gateway ist bereit!")
                break
        except:
            print(f"⏳ Warte auf API Gateway... ({i+1}/{max_retries})")
            time.sleep(2)
    
    # Tests ausführen
    test_suite = TestSystemIntegration()
    
    tests = [
        ("API Gateway Health Check", test_suite.test_api_gateway_health),
        ("Market Data Endpoint", test_suite.test_market_data_endpoint),
        ("Database Connection", test_suite.test_database_connection),
        ("Redis Connection", test_suite.test_redis_connection),
        ("Strategy Execution", test_suite.test_strategy_execution),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            test_func()
            print(f"✅ {test_name} erfolgreich")
            results.append((test_name, True, None))
        except Exception as e:
            print(f"❌ {test_name} fehlgeschlagen: {e}")
            results.append((test_name, False, str(e)))
    
    # Summary
    passed = sum(1 for _, success, _ in results if success)
    total = len(results)
    print(f"\n📊 Test Summary: {passed}/{total} tests passed")
    
    return results


if __name__ == "__main__":
    run_system_tests()
