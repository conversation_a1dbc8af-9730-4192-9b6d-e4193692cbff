import psycopg2
import getpass

def test_connection():
    try:
        # Passwort sicher abfragen
        password = getpass.getpass("PostgreSQL Passwort für postgres: ")

        # Verbindung aufbauen
        conn = psycopg2.connect(
            host="localhost",
            database="postgres",  # Standard-DB zum Anlegen neuer DBs
            user="postgres",
            password=password,
            port="5432"
        )
        # Autocommit aktivieren, damit CREATE DATABASE außerhalb einer Transaktion läuft
        conn.autocommit = True

        cursor = conn.cursor()

        # Version abfragen
        cursor.execute('SELECT version();')
        version = cursor.fetchone()
        print(f"✅ PostgreSQL-Verbindung erfolgreich!\nVersion: {version[0]}")

        # Neue Datenbank anlegen
        cursor.execute("CREATE DATABASE v5trader;")
        print("✅ Datenbank v5trader erfolgreich erstellt!")

        cursor.close()
        conn.close()

    except psycopg2.errors.DuplicateDatabase:
        print("ℹ️ Datenbank v5trader existiert bereits")
    except Exception as error:
        print(f"❌ Fehler: {error}")

if __name__ == "__main__":
    test_connection()
