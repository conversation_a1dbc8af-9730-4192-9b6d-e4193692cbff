"""
Integration tests for model components
"""
import pytest
import torch
import pandas as pd
import os
import sys

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.ensemble.advanced_transformer import AdvancedFinancialTransformer
from data.fin_data_module import FinancialDataModule


class TestModelIntegration:
    """Test suite for model integration"""
    
    def setup_method(self):
        """Setup test data and model"""
        # Create sample data
        self.batch_size = 4
        self.seq_len = 20
        self.n_features = 5
        
        # Sample model parameters
        self.model_params = {
            "input_features": self.n_features,
            "sequence_length": self.seq_len,
            "d_model": 64,  # Smaller for testing
            "n_heads": 4,
            "n_encoder_layers": 2,
            "dropout": 0.1,
            "cnn_filters": 32,
            "lstm_units": 64,
            "output_size": 1,
            "learning_rate": 1e-4,
            "weight_decay": 1e-5,
            "use_mixed_precision": False,  # Disable for testing
            "uncertainty_estimation": True,
            "ensemble_size": 3
        }
        
        # Create sample financial data
        n_samples = 100
        dates = pd.date_range('2023-01-01', periods=n_samples, freq='D')
        self.sample_data = pd.DataFrame({
            'Date': dates,
            'Open': 100 + torch.randn(n_samples).numpy() * 5,
            'High': 105 + torch.randn(n_samples).numpy() * 5,
            'Low': 95 + torch.randn(n_samples).numpy() * 5,
            'Close': 100 + torch.randn(n_samples).numpy() * 5,
            'Volume': 1000000 + torch.randint(0, 500000, (n_samples,)).numpy(),
        })
        
        # Add target
        self.sample_data['Target'] = (
            self.sample_data['Close'].shift(-1) > self.sample_data['Close']
        ).astype(float)
        self.sample_data = self.sample_data.dropna()
        
    def test_model_initialization(self):
        """Test model can be initialized with correct parameters"""
        model = AdvancedFinancialTransformer(**self.model_params)
        
        # Check model attributes
        assert model.input_features == self.n_features
        assert model.sequence_length == self.seq_len
        assert model.d_model == 64
        assert model.uncertainty_estimation == True
        
        # Check model is in training mode
        assert model.training
        
    def test_model_forward_pass(self):
        """Test model forward pass with sample data"""
        model = AdvancedFinancialTransformer(**self.model_params)
        
        # Create sample input
        x = torch.randn(self.batch_size, self.seq_len, self.n_features)
        
        # Forward pass
        outputs = model(x)
        
        # Check output structure
        assert isinstance(outputs, dict)
        assert 'short_term' in outputs
        assert 'medium_term' in outputs
        assert 'long_term' in outputs
        
        # Check output shapes
        for head_name, output in outputs.items():
            if head_name in ['short_term', 'medium_term', 'long_term']:
                assert output.shape == (self.batch_size, 1)
                
    def test_model_with_uncertainty(self):
        """Test model forward pass with uncertainty estimation"""
        model = AdvancedFinancialTransformer(**self.model_params)
        
        x = torch.randn(self.batch_size, self.seq_len, self.n_features)
        
        # Forward pass with uncertainty
        outputs = model(x, return_uncertainty=True)
        
        # Check uncertainty outputs
        if model.uncertainty_estimation:
            assert 'ensemble_mean' in outputs
            assert 'epistemic_uncertainty' in outputs
            assert 'predictions' in outputs
            
    def test_model_with_attention(self):
        """Test model forward pass with attention weights"""
        model = AdvancedFinancialTransformer(**self.model_params)
        
        x = torch.randn(self.batch_size, self.seq_len, self.n_features)
        
        # Forward pass with attention
        outputs = model(x, return_attention=True)
        
        # Check attention outputs
        assert 'attention_weights' in outputs
        assert isinstance(outputs['attention_weights'], list)
        
    def test_data_module_model_integration(self):
        """Test integration between DataModule and Model"""
        # Prepare data
        features = self.sample_data[['Open', 'High', 'Low', 'Close', 'Volume']]
        targets = self.sample_data['Target']
        
        dm = FinancialDataModule(
            features=features,
            targets=targets,
            seq_len=self.seq_len,
            batch_size=self.batch_size,
            val_split=0.2
        )
        dm.setup()
        
        # Create model
        model = AdvancedFinancialTransformer(**self.model_params)
        
        # Test with real data batch
        train_loader = dm.train_dataloader()
        batch = next(iter(train_loader))
        x, y = batch
        
        # Forward pass
        outputs = model(x)
        
        # Check shapes match
        assert x.shape == (self.batch_size, self.seq_len, self.n_features)
        assert y.shape == (self.batch_size,)
        assert outputs['short_term'].shape == (self.batch_size, 1)
        
    def test_training_step(self):
        """Test model training step"""
        model = AdvancedFinancialTransformer(**self.model_params)
        
        # Create sample batch
        x = torch.randn(self.batch_size, self.seq_len, self.n_features)
        y = torch.randn(self.batch_size, 1)
        batch = (x, y)
        
        # Training step
        loss = model.training_step(batch, 0)
        
        # Check loss is computed
        assert isinstance(loss, torch.Tensor)
        assert loss.requires_grad
        assert loss.item() > 0
        
    def test_validation_step(self):
        """Test model validation step"""
        model = AdvancedFinancialTransformer(**self.model_params)
        
        # Create sample batch
        x = torch.randn(self.batch_size, self.seq_len, self.n_features)
        y = torch.randn(self.batch_size, 1)
        batch = (x, y)
        
        # Validation step
        loss = model.validation_step(batch, 0)
        
        # Check loss is computed
        assert isinstance(loss, torch.Tensor)
        assert not loss.requires_grad  # Should not require grad in validation
        assert loss.item() > 0
        
    def test_optimizer_configuration(self):
        """Test optimizer configuration"""
        model = AdvancedFinancialTransformer(**self.model_params)
        
        # Mock trainer for optimizer configuration
        class MockTrainer:
            estimated_stepping_batches = 1000
            
        model.trainer = MockTrainer()
        
        # Configure optimizers
        optimizer_config = model.configure_optimizers()
        
        # Check optimizer configuration
        assert 'optimizer' in optimizer_config
        assert 'lr_scheduler' in optimizer_config
        
        optimizer = optimizer_config['optimizer']
        scheduler_config = optimizer_config['lr_scheduler']
        
        assert hasattr(optimizer, 'param_groups')
        assert 'scheduler' in scheduler_config
        assert scheduler_config['interval'] == 'step'
        
    def test_model_device_compatibility(self):
        """Test model works on both CPU and GPU (if available)"""
        model = AdvancedFinancialTransformer(**self.model_params)
        
        # Test on CPU
        x_cpu = torch.randn(self.batch_size, self.seq_len, self.n_features)
        outputs_cpu = model(x_cpu)
        assert outputs_cpu['short_term'].device.type == 'cpu'
        
        # Test on GPU if available
        if torch.cuda.is_available():
            model_gpu = model.cuda()
            x_gpu = x_cpu.cuda()
            outputs_gpu = model_gpu(x_gpu)
            assert outputs_gpu['short_term'].device.type == 'cuda'


if __name__ == "__main__":
    # Run tests
    test_suite = TestModelIntegration()
    test_suite.setup_method()
    
    print("🧪 Running model integration tests...")
    
    try:
        test_suite.test_model_initialization()
        print("✅ Model initialization test passed")
        
        test_suite.test_model_forward_pass()
        print("✅ Model forward pass test passed")
        
        test_suite.test_model_with_uncertainty()
        print("✅ Model uncertainty test passed")
        
        test_suite.test_model_with_attention()
        print("✅ Model attention test passed")
        
        test_suite.test_data_module_model_integration()
        print("✅ DataModule-Model integration test passed")
        
        test_suite.test_training_step()
        print("✅ Training step test passed")
        
        test_suite.test_validation_step()
        print("✅ Validation step test passed")
        
        test_suite.test_optimizer_configuration()
        print("✅ Optimizer configuration test passed")
        
        test_suite.test_model_device_compatibility()
        print("✅ Device compatibility test passed")

        print("\n🎉 All model integration tests passed!")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        raise
