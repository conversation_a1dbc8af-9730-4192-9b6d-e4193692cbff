# 🚀 V5_Trader - AI Trading Platform

> **Enterprise-grade AI trading system with MLflow, Prometheus & Grafana integration**

[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![PyTorch](https://img.shields.io/badge/PyTorch-2.0+-red.svg)](https://pytorch.org/)
[![MLflow](https://img.shields.io/badge/MLflow-2.8+-green.svg)](https://mlflow.org/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)

## ⚡ Quick Start

```bash
# 1. Clone and setup
git clone <repository-url>
cd V5_Trader
python -m venv venv && venv\Scripts\activate

# 2. Install dependencies
pip install -r requirements.txt

# 3. Start complete system
python start_v5trader.py

# 4. Access interfaces
# Dashboard: http://localhost:8000
# MLflow:    http://localhost:5000
# Grafana:   http://localhost:3000
```

## 🏗️ Architecture

```mermaid
graph TB
    A[Market Data] --> B[Data Pipeline]
    B --> C[AI Models]
    C --> D[Trading Strategies]
    D --> E[API Gateway]
    E --> F[WebSocket Server]
    F --> G[Web Dashboard]
    
    C --> H[MLflow]
    E --> I[Prometheus]
    I --> J[Grafana]
```

## 🎯 Core Features

| Component | Technology | Purpose |
|-----------|------------|---------|
| **AI Models** | PyTorch Lightning | 24M parameter transformer with uncertainty |
| **Data Pipeline** | Pandas + Yahoo Finance | Real-time market data processing |
| **Trading Engine** | Custom Strategies | Technical analysis & backtesting |
| **API Gateway** | FastAPI | REST API with Prometheus metrics |
| **Real-time Data** | WebSockets | Live market data streaming |
| **ML Operations** | MLflow | Experiment tracking & model registry |
| **Monitoring** | Prometheus + Grafana | System metrics & visualization |
| **Web Interface** | HTML5 + JavaScript | Real-time trading dashboard |

## 📊 Performance Metrics

- **Model Inference**: <5ms latency
- **API Response**: <50ms P95
- **Data Processing**: 774 data points (2 years)
- **Real-time Updates**: 2-second intervals
- **System Uptime**: >99.9% target

## 🛠️ Development

### Configuration-Based Training
```bash
# Edit config/model_config.yaml
python train_model_clean.py
```

### Testing
```bash
# Run all tests
python tests/test_integration_consolidated.py

# Validate system
python test_complete_system.py
```

### Monitoring
```bash
# System health
python tools/system_monitor.py

# Maintenance
python tools/maintenance.py full
```

## 📁 Project Structure

```
V5_Trader/
├── config/                 # Configuration management
├── data/                   # Market data & processing
├── models/                 # AI models & components
├── strategies/             # Trading strategies
├── services/               # API Gateway & WebSocket
├── monitoring/             # Prometheus & Grafana
├── tools/                  # System utilities
├── tests/                  # Comprehensive test suite
└── static/                 # Web dashboard
```

## 🐳 Docker Deployment

```bash
# Start monitoring stack
docker-compose up -d postgres redis mlflow prometheus grafana

# Full system
docker-compose up -d
```

## 📚 Documentation

- **[Installation Guide](docs/INSTALLATION.md)** - Detailed setup instructions
- **[Monitoring Guide](MONITORING_INTEGRATION_GUIDE.md)** - MLflow, Prometheus & Grafana
- **[System Guide](SYSTEM_GUIDE.md)** - Complete system documentation
- **[API Documentation](http://localhost:8000/docs)** - Interactive API docs

## 🤝 Contributing

1. **Fork** the repository
2. **Create** feature branch (`git checkout -b feature/amazing-feature`)
3. **Test** your changes (`python tests/test_integration_consolidated.py`)
4. **Commit** changes (`git commit -m 'Add amazing feature'`)
5. **Push** to branch (`git push origin feature/amazing-feature`)
6. **Create** Pull Request

## 🎯 Use Cases

### For Students & Interns
- **Modern Tech Stack**: PyTorch, FastAPI, Docker, MLflow
- **Production Patterns**: Microservices, monitoring, testing
- **Real-world Application**: Financial technology implementation

### For Developers
- **Scalable Architecture**: Microservices with Docker
- **ML Operations**: Complete MLOps pipeline
- **Monitoring**: Enterprise-grade observability

### For Traders
- **AI-Powered Signals**: Advanced transformer models
- **Real-time Data**: Live market data streaming
- **Risk Management**: Portfolio tracking & analysis

## 🔧 Configuration

### Model Parameters
```yaml
# config/model_config.yaml
model:
  architecture:
    d_model: 512
    n_heads: 8
    n_encoder_layers: 6
  training:
    learning_rate: 1e-4
    batch_size: 32
    max_epochs: 100
```

### Environment
```bash
# .env
POSTGRES_PASSWORD=secure_password
MLFLOW_TRACKING_URI=http://localhost:5000
GRAFANA_PASSWORD=admin_password
```

## 📈 Roadmap

- [ ] **Options Trading** - Derivatives support
- [ ] **Cryptocurrency** - Digital asset integration  
- [ ] **Mobile App** - React Native dashboard
- [ ] **Advanced Models** - Reinforcement learning
- [ ] **Multi-Broker** - Multiple broker integration

## 🆘 Support

- **Issues**: [GitHub Issues](https://github.com/username/v5trader/issues)
- **Discussions**: [GitHub Discussions](https://github.com/username/v5trader/discussions)
- **Documentation**: [docs/](docs/)

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.

---

**Built with ❤️ for the AI trading community**

*Perfect for internships, portfolios, and production trading systems*
