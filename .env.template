# V5_Trader Environment Configuration Template
# Copy this file to .env and update the values

# Database Configuration
POSTGRES_PASSWORD=v5trader_secure_2024
POSTGRES_DB=v5trader
POSTGRES_USER=postgres

# Redis Configuration
REDIS_PASSWORD=v5trader_redis_2024

# Grafana Configuration
GRAFANA_USER=admin
GRAFANA_PASSWORD=v5trader_grafana_2024

# MLflow Configuration
MLFLOW_TRACKING_URI=http://localhost:5000
MLFLOW_BACKEND_STORE_URI=********************************************************/v5trader
MLFLOW_DEFAULT_ARTIFACT_ROOT=/mlflow/artifacts

# API Gateway Configuration
API_SECRET_KEY=v5trader_api_secret_key_2024_very_secure
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# External APIs (Optional)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
YAHOO_FINANCE_API_KEY=your_yahoo_finance_key_here

# Monitoring Configuration
PROMETHEUS_RETENTION_TIME=200h
GRAFANA_INSTALL_PLUGINS=grafana-piechart-panel

# Development Configuration
DEBUG=false
LOG_LEVEL=INFO
ENVIRONMENT=production

# Security Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]
ALLOWED_HOSTS=["localhost", "127.0.0.1"]

# Performance Configuration
REDIS_MAX_CONNECTIONS=100
POSTGRES_MAX_CONNECTIONS=100
API_WORKERS=4
API_TIMEOUT=30

# Backup Configuration
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
