#!/usr/bin/env python3
"""
Master-Launcher für das komplette V5 Trader System
Startet alle Services und überwacht den Systemstatus
"""
import os
import sys
import time
import subprocess
import threading
import webbrowser
from pathlib import Path
import signal
import atexit

class V5TraderLauncher:
    """Master-Launcher für das komplette V5 Trader System"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.services_started = []
        self.processes = []
        self.running = True
        
        # Register cleanup on exit
        atexit.register(self.cleanup)
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\n🛑 Signal {signum} empfangen, beende System...")
        self.running = False
        self.cleanup()
        sys.exit(0)
    
    def check_prerequisites(self):
        """Überprüft alle Voraussetzungen"""
        print("🔍 Überprüfe Systemvoraussetzungen...")
        
        checks = {
            "Python": self.check_python(),
            "Virtual Environment": self.check_venv(),
            "Dependencies": self.check_dependencies(),
            "Data Files": self.check_data_files(),
            "Project Structure": self.check_project_structure()
        }
        
        all_good = True
        for service, status in checks.items():
            if status:
                print(f"  ✅ {service}")
            else:
                print(f"  ❌ {service}")
                all_good = False
        
        return all_good
    
    def check_python(self):
        """Check Python version"""
        try:
            version = sys.version_info
            return version.major == 3 and version.minor >= 8
        except:
            return False
    
    def check_venv(self):
        """Check if virtual environment exists"""
        return (self.project_root / "venv").exists()
    
    def check_dependencies(self):
        """Check if requirements.txt exists"""
        return (self.project_root / "requirements.txt").exists()
    
    def check_data_files(self):
        """Check if data files exist"""
        return (self.project_root / "data" / "market_data.csv").exists()
    
    def check_project_structure(self):
        """Check if all required directories exist"""
        required_dirs = ["models", "strategies", "services", "tools", "tests", "static"]
        return all((self.project_root / dir_name).exists() for dir_name in required_dirs)
    
    def install_dependencies(self):
        """Install missing dependencies"""
        print("📦 Installiere/Aktualisiere Dependencies...")
        
        try:
            # Activate virtual environment and install dependencies
            if sys.platform == "win32":
                pip_cmd = str(self.project_root / "venv" / "Scripts" / "pip.exe")
            else:
                pip_cmd = str(self.project_root / "venv" / "bin" / "pip")
            
            if not Path(pip_cmd).exists():
                pip_cmd = "pip"  # Fallback to system pip
            
            # Install essential packages
            essential_packages = [
                "websockets",
                "requests", 
                "pandas",
                "torch",
                "pytorch-lightning",
                "mlflow"
            ]
            
            for package in essential_packages:
                try:
                    result = subprocess.run([pip_cmd, "install", package], 
                                          capture_output=True, text=True, timeout=60)
                    if result.returncode == 0:
                        print(f"  ✅ {package} installiert")
                    else:
                        print(f"  ⚠️ {package} Installation fehlgeschlagen")
                except subprocess.TimeoutExpired:
                    print(f"  ⚠️ {package} Installation Timeout")
                except Exception as e:
                    print(f"  ⚠️ {package} Fehler: {e}")
            
            return True
        except Exception as e:
            print(f"  ❌ Dependency Installation fehlgeschlagen: {e}")
            return False
    
    def start_websocket_server(self):
        """Startet WebSocket Server in separatem Prozess"""
        def run_websocket():
            try:
                os.chdir(self.project_root)
                process = subprocess.Popen([
                    sys.executable, "services/websocket_server.py"
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                self.processes.append(process)
                return process
            except Exception as e:
                print(f"  ❌ WebSocket Server Fehler: {e}")
                return None
        
        print("🚀 Starte WebSocket Server...")
        process = run_websocket()
        if process:
            self.services_started.append("websocket")
            print("  ✅ WebSocket Server gestartet")
            return True
        return False
    
    def start_system_monitor(self):
        """Startet System-Monitor in separatem Prozess"""
        def run_monitor():
            try:
                os.chdir(self.project_root)
                # Run monitor in background mode
                process = subprocess.Popen([
                    sys.executable, "-c", 
                    "from tools.system_monitor import SystemMonitor; "
                    "import time; "
                    "monitor = SystemMonitor(); "
                    "print('System Monitor gestartet'); "
                    "while True: time.sleep(30)"
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                self.processes.append(process)
                return process
            except Exception as e:
                print(f"  ❌ System Monitor Fehler: {e}")
                return None
        
        print("📊 Starte System Monitor...")
        process = run_monitor()
        if process:
            self.services_started.append("monitor")
            print("  ✅ System Monitor gestartet")
            return True
        return False
    
    def wait_for_services(self):
        """Wartet bis WebSocket Service bereit ist"""
        print("⏳ Warte auf Service-Bereitschaft...")
        
        # Check WebSocket server
        import socket
        max_retries = 15
        for i in range(max_retries):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', 8765))
                sock.close()
                
                if result == 0:
                    print("  ✅ WebSocket Server bereit")
                    break
            except:
                pass
            
            if i < max_retries - 1:
                print(f"  ⏳ Warte auf WebSocket Server... ({i+1}/{max_retries})")
                time.sleep(2)
            else:
                print("  ⚠️ WebSocket Server nicht erreichbar")
    
    def open_dashboard(self):
        """Öffnet das Dashboard im Browser"""
        try:
            dashboard_path = self.project_root / "static" / "dashboard.html"
            if dashboard_path.exists():
                dashboard_url = f"file://{dashboard_path.absolute()}"
                webbrowser.open(dashboard_url)
                print("🌐 Dashboard geöffnet im Browser")
                return True
            else:
                print("❌ Dashboard-Datei nicht gefunden")
                return False
        except Exception as e:
            print(f"❌ Dashboard konnte nicht geöffnet werden: {e}")
            return False
    
    def run_system_tests(self):
        """Führt System-Tests aus"""
        print("🧪 Führe System-Tests aus...")
        
        try:
            os.chdir(self.project_root)
            result = subprocess.run([
                sys.executable, "test_complete_system.py"
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("  ✅ System-Tests erfolgreich")
                return True
            else:
                print("  ⚠️ Einige System-Tests fehlgeschlagen")
                print(f"  Details: {result.stderr}")
                return False
        except subprocess.TimeoutExpired:
            print("  ⚠️ System-Tests Timeout")
            return False
        except Exception as e:
            print(f"  ❌ System-Tests Fehler: {e}")
            return False
    
    def launch_system(self):
        """Startet das komplette System"""
        print("🚀 V5 TRADER SYSTEM LAUNCHER")
        print("=" * 60)
        
        # Voraussetzungen prüfen
        if not self.check_prerequisites():
            print("\n⚠️ Einige Systemvoraussetzungen fehlen!")
            print("Versuche automatische Installation...")
            
            if not self.install_dependencies():
                print("❌ Automatische Installation fehlgeschlagen!")
                print("Bitte installiere die fehlenden Komponenten manuell.")
                return False
        
        # System-Tests ausführen
        if not self.run_system_tests():
            print("⚠️ System-Tests nicht vollständig erfolgreich, fahre trotzdem fort...")
        
        # Services starten
        services_started = 0
        
        if self.start_websocket_server():
            services_started += 1
        
        if self.start_system_monitor():
            services_started += 1
        
        # Auf Services warten
        self.wait_for_services()
        
        # Dashboard öffnen
        dashboard_opened = self.open_dashboard()
        
        print("\n" + "=" * 60)
        print("🎉 V5 TRADER SYSTEM GESTARTET!")
        print("=" * 60)
        print(f"📊 Services gestartet: {services_started}")
        print(f"🌐 Dashboard: {'✅ Geöffnet' if dashboard_opened else '❌ Fehler'}")
        print("\n🔗 Verfügbare URLs:")
        print("   • Dashboard:     file://" + str(self.project_root / "static" / "dashboard.html"))
        print("   • WebSocket:     ws://localhost:8765")
        print("\n🎮 Verfügbare Befehle:")
        print("   • Model Training:    python train_and_register.py")
        print("   • Strategy Testing:  python test_trading_strategy.py")
        print("   • System Monitor:    python tools/system_monitor.py")
        print("   • Dashboard:         python tools/dashboard.py")
        print("\n🛑 Steuerung:")
        print("   • System stoppen: Ctrl+C")
        print("   • Status prüfen:  Prozesse laufen im Hintergrund")
        print("=" * 60)
        
        return True
    
    def monitor_system(self):
        """Überwacht das System während der Laufzeit"""
        print("\n🔍 System-Überwachung aktiv...")
        print("Drücke Ctrl+C zum Beenden")
        
        try:
            while self.running:
                # Check if processes are still running
                active_processes = []
                for process in self.processes:
                    if process.poll() is None:  # Process is still running
                        active_processes.append(process)
                    else:
                        print(f"⚠️ Prozess beendet (Exit Code: {process.returncode})")
                
                self.processes = active_processes
                
                if not self.processes:
                    print("⚠️ Alle Prozesse beendet")
                    break
                
                time.sleep(10)  # Check every 10 seconds
                
        except KeyboardInterrupt:
            print("\n🛑 Beende System...")
            self.running = False
    
    def cleanup(self):
        """Räumt Services auf"""
        if not hasattr(self, '_cleanup_done'):
            self._cleanup_done = True
            print("\n🧹 Räume System auf...")
            
            # Terminate all processes
            for process in self.processes:
                try:
                    if process.poll() is None:  # Process is still running
                        process.terminate()
                        # Wait a bit for graceful shutdown
                        try:
                            process.wait(timeout=5)
                        except subprocess.TimeoutExpired:
                            process.kill()  # Force kill if necessary
                except Exception as e:
                    print(f"  ⚠️ Fehler beim Beenden eines Prozesses: {e}")
            
            print("  ✅ Alle Prozesse beendet")
            print("🏁 V5 Trader System beendet!")

def main():
    """Main entry point"""
    launcher = V5TraderLauncher()
    
    try:
        success = launcher.launch_system()
        if success:
            launcher.monitor_system()
    except KeyboardInterrupt:
        print("\n🛑 System wird beendet...")
    except Exception as e:
        print(f"\n❌ Unerwarteter Fehler: {e}")
    finally:
        launcher.cleanup()

if __name__ == "__main__":
    main()
