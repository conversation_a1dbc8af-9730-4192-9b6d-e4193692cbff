#!/usr/bin/env python3
"""
Comprehensive test for trading strategies in V5_Trader
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import os
import sys

# Add strategies to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategies.technical_indicators import SimpleMovingAverageStrategy, TechnicalIndicators


def create_sample_market_data(days=100, start_price=100, volatility=0.02):
    """Creates realistic sample market data for testing"""
    dates = pd.date_range(start='2023-01-01', periods=days, freq='D')
    
    # Generate price series with random walk
    returns = np.random.normal(0, volatility, days)
    prices = [start_price]
    
    for i in range(1, days):
        new_price = prices[-1] * (1 + returns[i])
        prices.append(new_price)
    
    # Create OHLC data
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        # Generate realistic OHLC from close price
        high = close * (1 + abs(np.random.normal(0, 0.01)))
        low = close * (1 - abs(np.random.normal(0, 0.01)))
        open_price = prices[i-1] if i > 0 else close
        volume = np.random.randint(1000000, 10000000)
        
        data.append({
            'Date': date,
            'Open': open_price,
            'High': max(open_price, high, close),
            'Low': min(open_price, low, close),
            'Close': close,
            'Volume': volume
        })
    
    return pd.DataFrame(data)


def test_technical_indicators():
    """Test technical indicators calculations"""
    print("🧪 Testing Technical Indicators...")
    
    # Create test data
    data = create_sample_market_data(50, 100, 0.02)
    indicators = TechnicalIndicators()
    
    # Test SMA
    sma_10 = indicators.calculate_sma(data['Close'], 10)
    assert len(sma_10) == len(data)
    assert not pd.isna(sma_10.iloc[-1])  # Last value should not be NaN
    print("✅ SMA calculation works")
    
    # Test EMA
    ema_10 = indicators.calculate_ema(data['Close'], 10)
    assert len(ema_10) == len(data)
    assert not pd.isna(ema_10.iloc[-1])
    print("✅ EMA calculation works")
    
    # Test RSI
    rsi = indicators.calculate_rsi(data['Close'], 14)
    assert len(rsi) == len(data)
    # RSI should be between 0 and 100
    valid_rsi = rsi.dropna()
    assert all(0 <= val <= 100 for val in valid_rsi)
    print("✅ RSI calculation works")
    
    # Test Bollinger Bands
    upper, middle, lower = indicators.calculate_bollinger_bands(data['Close'], 20, 2)
    assert len(upper) == len(data)
    # Upper should be >= middle >= lower
    valid_data = ~(pd.isna(upper) | pd.isna(middle) | pd.isna(lower))
    assert all(upper[valid_data] >= middle[valid_data])
    assert all(middle[valid_data] >= lower[valid_data])
    print("✅ Bollinger Bands calculation works")
    
    # Test MACD
    macd_line, signal_line, histogram = indicators.calculate_macd(data['Close'])
    assert len(macd_line) == len(data)
    assert len(signal_line) == len(data)
    assert len(histogram) == len(data)
    print("✅ MACD calculation works")


def test_sma_strategy():
    """Test Simple Moving Average Strategy"""
    print("\n📈 Testing SMA Strategy...")
    
    # Create test data with clear trend
    data = create_sample_market_data(100, 100, 0.01)
    
    # Add artificial trend for testing
    trend = np.linspace(0, 20, len(data))
    data['Close'] = data['Close'] + trend
    
    # Initialize strategy
    strategy_params = {"short_window": 5, "long_window": 20}
    strategy = SimpleMovingAverageStrategy(strategy_params)
    
    print(f"Initial balance: ${strategy.balance:,.2f}")
    
    # Simulate trading over the data
    signals_generated = 0
    trades_executed = 0
    
    for i in range(25, len(data)):  # Start after enough data for indicators
        current_data = data.iloc[:i+1].copy()
        current_price = current_data['Close'].iloc[-1]
        
        # Generate signal
        signal = strategy.generate_signals(current_data)
        
        if signal['signal'] != 'HOLD':
            signals_generated += 1
            position_size = strategy.calculate_position_size(signal, current_price)
            
            if signal['signal'] == 'BUY' and position_size > 0:
                strategy.execute_trade('TEST', 'BUY', position_size, current_price)
                trades_executed += 1
                
            elif signal['signal'] == 'SELL' and 'TEST' in strategy.positions:
                # Sell all positions
                quantity = strategy.positions['TEST']
                strategy.execute_trade('TEST', 'SELL', quantity, current_price)
                trades_executed += 1
    
    # Calculate final portfolio value
    final_prices = {'TEST': data['Close'].iloc[-1]}
    final_value = strategy.get_portfolio_value(final_prices)
    
    # Get performance metrics
    metrics = strategy.get_performance_metrics()
    
    print(f"Signals generated: {signals_generated}")
    print(f"Trades executed: {trades_executed}")
    print(f"Final balance: ${strategy.balance:,.2f}")
    print(f"Final portfolio value: ${final_value:,.2f}")
    print(f"Total return: {((final_value - 100000) / 100000) * 100:.2f}%")
    print(f"Total trades: {metrics.get('total_trades', 0)}")
    
    # Assertions
    assert signals_generated > 0, "Strategy should generate some signals"
    assert trades_executed > 0, "Strategy should execute some trades"
    assert final_value > 0, "Portfolio should have positive value"
    
    print("✅ SMA Strategy test completed successfully")
    
    return strategy, metrics


def test_strategy_with_real_data():
    """Test strategy with real market data if available"""
    print("\n📊 Testing with Real Market Data...")
    
    if not os.path.exists('data/market_data.csv'):
        print("⚠️ No real market data found, skipping this test")
        return
    
    try:
        # Load real data
        df = pd.read_csv('data/market_data.csv', parse_dates=['Date'])
        aapl_data = df[df['Ticker'] == 'AAPL'].copy()
        
        if len(aapl_data) < 50:
            print("⚠️ Not enough real data for testing")
            return
        
        print(f"Using {len(aapl_data)} days of AAPL data")
        
        # Test strategy
        strategy_params = {"short_window": 10, "long_window": 30}
        strategy = SimpleMovingAverageStrategy(strategy_params)
        
        trades_executed = 0
        
        for i in range(35, len(aapl_data)):  # Start after enough data
            current_data = aapl_data.iloc[:i+1].copy()
            current_price = current_data['Close'].iloc[-1]
            
            signal = strategy.generate_signals(current_data)
            
            if signal['signal'] == 'BUY':
                position_size = strategy.calculate_position_size(signal, current_price)
                if position_size > 0:
                    strategy.execute_trade('AAPL', 'BUY', position_size, current_price)
                    trades_executed += 1
                    
            elif signal['signal'] == 'SELL' and 'AAPL' in strategy.positions:
                quantity = strategy.positions['AAPL']
                strategy.execute_trade('AAPL', 'SELL', quantity, current_price)
                trades_executed += 1
        
        # Final results
        final_prices = {'AAPL': aapl_data['Close'].iloc[-1]}
        final_value = strategy.get_portfolio_value(final_prices)
        
        print(f"Real data test - Trades: {trades_executed}")
        print(f"Final portfolio value: ${final_value:,.2f}")
        print(f"Return: {((final_value - 100000) / 100000) * 100:.2f}%")
        print("✅ Real data test completed")
        
    except Exception as e:
        print(f"❌ Error testing with real data: {e}")


def run_comprehensive_strategy_test():
    """Run all strategy tests"""
    print("🚀 Starting Comprehensive Trading Strategy Tests")
    print("=" * 60)
    
    try:
        # Test technical indicators
        test_technical_indicators()
        
        # Test SMA strategy
        strategy, metrics = test_sma_strategy()
        
        # Test with real data
        test_strategy_with_real_data()
        
        print("\n" + "=" * 60)
        print("🎉 ALL STRATEGY TESTS PASSED!")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Strategy test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_comprehensive_strategy_test()
    sys.exit(0 if success else 1)
