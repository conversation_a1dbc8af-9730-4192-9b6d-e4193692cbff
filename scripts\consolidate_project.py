#!/usr/bin/env python3
"""
Project Consolidation Script for V5_Trader
Removes duplicates, organizes files, and cleans up the project structure
"""
import os
import shutil
import logging
from pathlib import Path
from typing import List, Dict, Tuple
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ProjectConsolidator:
    """Consolidates and cleans up the V5_Trader project structure"""
    
    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path(__file__).parent.parent
        self.backup_dir = self.project_root / "backups" / "consolidation_backup"
        self.changes_log = []
        
    def run_consolidation(self) -> Dict:
        """Run complete project consolidation"""
        logger.info("🚀 Starting V5_Trader project consolidation...")
        
        # Create backup
        self._create_backup()
        
        # Consolidation steps
        steps = [
            ("Remove duplicate directories", self._remove_duplicate_directories),
            ("Consolidate monitoring configs", self._consolidate_monitoring),
            ("Organize documentation", self._organize_documentation),
            ("Move utility scripts", self._organize_scripts),
            ("Clean up root directory", self._clean_root_directory),
            ("Update references", self._update_references),
            ("Validate structure", self._validate_structure)
        ]
        
        results = {}
        for step_name, step_func in steps:
            logger.info(f"📋 {step_name}...")
            try:
                result = step_func()
                results[step_name] = {"status": "success", "details": result}
                logger.info(f"✅ {step_name} completed")
            except Exception as e:
                logger.error(f"❌ {step_name} failed: {e}")
                results[step_name] = {"status": "error", "error": str(e)}
        
        # Save consolidation report
        self._save_report(results)
        
        logger.info("🎉 Project consolidation completed!")
        return results
    
    def _create_backup(self):
        """Create backup of current state"""
        if self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)
        
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Backup critical directories
        backup_targets = [
            "services",
            "monitoring", 
            "docs",
            "*.md",
            "*.py"
        ]
        
        for target in backup_targets:
            if "*" in target:
                # Handle glob patterns
                for file_path in self.project_root.glob(target):
                    if file_path.is_file():
                        shutil.copy2(file_path, self.backup_dir / file_path.name)
            else:
                source = self.project_root / target
                if source.exists():
                    if source.is_dir():
                        shutil.copytree(source, self.backup_dir / target)
                    else:
                        shutil.copy2(source, self.backup_dir / target)
        
        logger.info(f"📦 Backup created at {self.backup_dir}")
    
    def _remove_duplicate_directories(self) -> List[str]:
        """Remove duplicate directories"""
        removed = []
        
        # Remove duplicate API Gateway directory
        api_gateway_old = self.project_root / "services" / "api-gateway"
        api_gateway_new = self.project_root / "services" / "api_gateway"
        
        if api_gateway_old.exists() and api_gateway_new.exists():
            # Keep the one with requirements.txt (more complete)
            if (api_gateway_new / "requirements.txt").exists():
                shutil.rmtree(api_gateway_old)
                removed.append("services/api-gateway")
                self.changes_log.append("Removed duplicate services/api-gateway directory")
            else:
                shutil.rmtree(api_gateway_new)
                api_gateway_old.rename(api_gateway_new)
                removed.append("services/api-gateway (renamed)")
                self.changes_log.append("Renamed services/api-gateway to services/api_gateway")
        
        return removed
    
    def _consolidate_monitoring(self) -> List[str]:
        """Consolidate monitoring configurations"""
        consolidated = []
        
        monitoring_dir = self.project_root / "monitoring"
        
        # Remove redundant monitoring subdirectory
        redundant_monitoring = monitoring_dir / "monitoring"
        if redundant_monitoring.exists():
            # Move contents up one level
            for item in redundant_monitoring.iterdir():
                target = monitoring_dir / item.name
                if not target.exists():
                    shutil.move(str(item), str(target))
                    consolidated.append(f"Moved {item.name} to monitoring/")
            
            shutil.rmtree(redundant_monitoring)
            consolidated.append("Removed redundant monitoring/monitoring directory")
            self.changes_log.append("Consolidated monitoring directory structure")
        
        # Consolidate Prometheus configs
        prometheus_configs = list(monitoring_dir.glob("**/prometheus.yml"))
        if len(prometheus_configs) > 1:
            # Keep the most complete one
            main_config = monitoring_dir / "prometheus" / "prometheus.yml"
            for config in prometheus_configs:
                if config != main_config and config.exists():
                    config.unlink()
                    consolidated.append(f"Removed duplicate {config}")
        
        return consolidated
    
    def _organize_documentation(self) -> List[str]:
        """Organize documentation into docs/ directory"""
        organized = []
        
        docs_dir = self.project_root / "docs"
        docs_dir.mkdir(exist_ok=True)
        
        # Documentation files to consolidate
        doc_files = {
            "README.md": "docs/00_README.md",
            "INSTALLATION.md": "docs/01_Installation.md",  # Already created
            "SYSTEM_GUIDE.md": "docs/02_System_Architecture.md",
            "MONITORING_INTEGRATION_GUIDE.md": "docs/03_Monitoring.md",
            "DASHBOARD_CONSOLIDATION.md": "docs/04_Dashboard.md",
            "IMPROVEMENTS_SUMMARY.md": "docs/05_Improvements.md",
            "FINAL_SUMMARY.md": "docs/06_Summary.md"
        }
        
        for source_name, target_path in doc_files.items():
            source = self.project_root / source_name
            target = self.project_root / target_path
            
            if source.exists() and not target.exists():
                shutil.move(str(source), str(target))
                organized.append(f"Moved {source_name} to {target_path}")
                self.changes_log.append(f"Organized documentation: {source_name} -> {target_path}")
        
        # Keep README_STREAMLINED.md as main README
        streamlined_readme = self.project_root / "README_STREAMLINED.md"
        main_readme = self.project_root / "README.md"
        
        if streamlined_readme.exists():
            if main_readme.exists():
                main_readme.unlink()
            streamlined_readme.rename(main_readme)
            organized.append("Set README_STREAMLINED.md as main README.md")
            self.changes_log.append("Updated main README to streamlined version")
        
        return organized
    
    def _organize_scripts(self) -> List[str]:
        """Organize utility scripts into scripts/ directory"""
        organized = []
        
        scripts_dir = self.project_root / "scripts"
        scripts_dir.mkdir(exist_ok=True)
        
        # Scripts to move
        script_patterns = [
            "debug_*.py",
            "check_*.py",
            "verify_*.py",
            "script.py",
            "chart_script.py"
        ]
        
        for pattern in script_patterns:
            for script_file in self.project_root.glob(pattern):
                if script_file.is_file() and script_file.parent == self.project_root:
                    target = scripts_dir / script_file.name
                    if not target.exists():
                        shutil.move(str(script_file), str(target))
                        organized.append(f"Moved {script_file.name} to scripts/")
                        self.changes_log.append(f"Organized script: {script_file.name}")
        
        return organized
    
    def _clean_root_directory(self) -> List[str]:
        """Clean up root directory"""
        cleaned = []
        
        # Files to remove from root
        cleanup_patterns = [
            "test_*.py",  # Move test files to tests/
            "*.pyc",
            "__pycache__"
        ]
        
        tests_dir = self.project_root / "tests"
        tests_dir.mkdir(exist_ok=True)
        
        for pattern in cleanup_patterns:
            for item in self.project_root.glob(pattern):
                if item.parent == self.project_root:
                    if pattern.startswith("test_") and item.suffix == ".py":
                        # Move test files to tests/
                        target = tests_dir / item.name
                        if not target.exists():
                            shutil.move(str(item), str(target))
                            cleaned.append(f"Moved {item.name} to tests/")
                    elif item.name == "__pycache__" or item.suffix == ".pyc":
                        # Remove cache files
                        if item.is_dir():
                            shutil.rmtree(item)
                        else:
                            item.unlink()
                        cleaned.append(f"Removed {item.name}")
        
        return cleaned
    
    def _update_references(self) -> List[str]:
        """Update file references after reorganization"""
        updated = []
        
        # Files that might contain references to moved files
        reference_files = [
            "docker-compose.yml",
            "start_v5trader.py",
            "launcher/system_launcher.py"
        ]
        
        # Reference updates
        updates = {
            "services/api-gateway": "services/api_gateway",
            "monitoring/monitoring/prometheus": "monitoring/prometheus",
            "tools/dashboard.py": "# Removed - use web dashboard",
        }
        
        for ref_file in reference_files:
            file_path = self.project_root / ref_file
            if file_path.exists():
                try:
                    content = file_path.read_text(encoding='utf-8')
                    original_content = content
                    
                    for old_ref, new_ref in updates.items():
                        if old_ref in content:
                            content = content.replace(old_ref, new_ref)
                    
                    if content != original_content:
                        file_path.write_text(content, encoding='utf-8')
                        updated.append(f"Updated references in {ref_file}")
                        self.changes_log.append(f"Updated file references in {ref_file}")
                        
                except Exception as e:
                    logger.warning(f"Could not update references in {ref_file}: {e}")
        
        return updated
    
    def _validate_structure(self) -> Dict:
        """Validate the new project structure"""
        validation = {
            "required_directories": [],
            "required_files": [],
            "issues": []
        }
        
        # Required directories
        required_dirs = [
            "config",
            "data", 
            "models",
            "services/api_gateway",
            "monitoring/prometheus",
            "docs",
            "scripts",
            "tests",
            "backtesting",
            "k8s"
        ]
        
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if full_path.exists():
                validation["required_directories"].append(f"✅ {dir_path}")
            else:
                validation["required_directories"].append(f"❌ {dir_path}")
                validation["issues"].append(f"Missing directory: {dir_path}")
        
        # Required files
        required_files = [
            "README.md",
            "requirements.txt",
            "docker-compose.yml",
            ".env.example",
            "config/model_config.yaml",
            "services/api_gateway/main.py",
            "docs/01_Installation.md"
        ]
        
        for file_path in required_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                validation["required_files"].append(f"✅ {file_path}")
            else:
                validation["required_files"].append(f"❌ {file_path}")
                validation["issues"].append(f"Missing file: {file_path}")
        
        return validation
    
    def _save_report(self, results: Dict):
        """Save consolidation report"""
        report = {
            "timestamp": str(Path(__file__).stat().st_mtime),
            "consolidation_results": results,
            "changes_log": self.changes_log,
            "backup_location": str(self.backup_dir)
        }
        
        report_file = self.project_root / "consolidation_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"📊 Consolidation report saved to {report_file}")

def main():
    """Main consolidation function"""
    consolidator = ProjectConsolidator()
    results = consolidator.run_consolidation()
    
    # Print summary
    print("\n" + "="*60)
    print("📊 CONSOLIDATION SUMMARY")
    print("="*60)
    
    for step, result in results.items():
        status = "✅" if result["status"] == "success" else "❌"
        print(f"{status} {step}")
        
        if result["status"] == "error":
            print(f"   Error: {result['error']}")
        elif "details" in result and result["details"]:
            for detail in result["details"][:3]:  # Show first 3 details
                print(f"   - {detail}")
            if len(result["details"]) > 3:
                print(f"   ... and {len(result['details']) - 3} more")
    
    print("\n🎉 Project consolidation completed!")
    print("📦 Backup created in backups/consolidation_backup/")
    print("📊 Full report saved in consolidation_report.json")

if __name__ == "__main__":
    main()
