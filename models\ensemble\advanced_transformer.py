import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
import mlflow
import mlflow.pytorch
import numpy as np
import pandas as pd
import time
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from torch.optim.lr_scheduler import OneCycle<PERSON>
from torchmetrics import MeanSquaredError, MeanAbsoluteError
import torch.quantization as quantization
from torch.amp import autocast, GradScaler
import warnings
warnings.filterwarnings("ignore")

class CrossDimensionAttention(nn.Module):
    """Implementierung der Cross-Dimension Attention aus Crossformer Architektur"""
    
    def __init__(self, d_model: int, n_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        self.d_model = d_model
        self.n_heads = n_heads
        self.head_dim = d_model // n_heads
        
        assert self.head_dim * n_heads == d_model
        
        self.q_proj = nn.Linear(d_model, d_model)
        self.k_proj = nn.Linear(d_model, d_model)  
        self.v_proj = nn.Linear(d_model, d_model)
        self.o_proj = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = self.head_dim ** -0.5
        
    def forward(self, query, key, value, mask=None):
        batch_size, seq_len, _ = query.size()
        
        # Multi-head projections
        Q = self.q_proj(query).view(batch_size, seq_len, self.n_heads, self.head_dim).transpose(1, 2)
        K = self.k_proj(key).view(batch_size, -1, self.n_heads, self.head_dim).transpose(1, 2)
        V = self.v_proj(value).view(batch_size, -1, self.n_heads, self.head_dim).transpose(1, 2)
        
        # Scaled dot-product attention
        scores = torch.matmul(Q, K.transpose(-2, -1)) * self.scale
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
            
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        context = torch.matmul(attn_weights, V)
        context = context.transpose(1, 2).contiguous().view(batch_size, seq_len, self.d_model)
        
        output = self.o_proj(context)
        return output, attn_weights

class AutoCorrelationMechanism(nn.Module):
    """Auto-Correlation Mechanismus aus Autoformer für periodische Abhängigkeiten"""
    
    def __init__(self, mask_flag=True, factor=1, scale=None, attention_dropout=0.1):
        super().__init__()
        self.factor = factor
        self.scale = scale
        self.mask_flag = mask_flag
        self.dropout = nn.Dropout(attention_dropout)

    def time_delay_agg_training(self, values, corr):
        """
        SpeedUp-Version des Auto-Correlation Mechanismus
        """
        head = values.shape[1]
        channel = values.shape[2]
        length = values.shape[3]
        
        # Finde Top-k Delays
        top_k = int(self.factor * np.log(length))
        mean_value = torch.mean(torch.mean(corr, dim=1), dim=1)
        index = torch.topk(torch.mean(mean_value, dim=0), top_k, dim=-1)[1]
        weights = torch.stack([mean_value[:, index[i]] for i in range(top_k)], dim=-1)
        
        # Update values basierend auf gefundenen Delays  
        tmp_corr = torch.softmax(weights, dim=-1)
        tmp_values = values
        delays_agg = torch.zeros_like(values).float()
        
        for i in range(top_k):
            pattern = torch.roll(tmp_values, -int(index[i]), -1)
            delays_agg = delays_agg + pattern * (tmp_corr[:, i].unsqueeze(1).unsqueeze(1).unsqueeze(1).repeat(1, head, channel, length))
            
        return delays_agg

    def forward(self, queries, keys, values):
        B, L, H, E = queries.shape
        _, S, _, D = values.shape
        
        if L > S:
            zeros = torch.zeros_like(queries[:, :(L - S), :]).float()
            values = torch.cat([values, zeros], dim=1)
            keys = torch.cat([keys, zeros], dim=1)
        else:
            values = values[:, :L, :, :]
            keys = keys[:, :L, :, :]

        # Auto-Correlation Berechnung
        q_fft = torch.fft.rfft(queries.permute(0, 2, 3, 1).contiguous(), dim=-1)
        k_fft = torch.fft.rfft(keys.permute(0, 2, 3, 1).contiguous(), dim=-1)
        res = q_fft * torch.conj(k_fft)
        corr = torch.fft.irfft(res, dim=-1)

        # Time Delay Aggregation
        V = self.time_delay_agg_training(values.permute(0, 2, 3, 1).contiguous(), corr).permute(0, 3, 1, 2)
        
        if self.scale is not None:
            V = self.scale * V
            
        return V.contiguous(), corr

class DecompositionLayer(nn.Module):
    """
    Series Decomposition aus Autoformer für Trend-Seasonal Separation
    """
    def __init__(self, kernel_size=25):
        super().__init__()
        self.kernel_size = kernel_size
        self.moving_avg = nn.AvgPool1d(kernel_size=kernel_size, stride=1, padding=kernel_size//2)

    def forward(self, x):
        # x shape: (batch, seq_len, features)
        batch_size, seq_len, features = x.shape

        # Moving average für Trend - process each feature separately
        x_transposed = x.transpose(-1, 1)  # (batch, features, seq_len)

        # Apply moving average
        trend_transposed = self.moving_avg(x_transposed)  # (batch, features, seq_len)
        trend = trend_transposed.transpose(-1, 1)  # (batch, seq_len, features)

        # Ensure trend has same shape as input
        if trend.shape[1] != seq_len:
            # Trim or pad to match input sequence length
            if trend.shape[1] > seq_len:
                trend = trend[:, :seq_len, :]
            else:
                # Pad with edge values
                pad_size = seq_len - trend.shape[1]
                pad_left = pad_size // 2
                pad_right = pad_size - pad_left
                trend = torch.nn.functional.pad(trend, (0, 0, pad_left, pad_right), mode='replicate')

        seasonal = x - trend
        return seasonal, trend

class AdvancedFinancialTransformer(pl.LightningModule):
    """
    State-of-the-Art Financial Time Series Transformer
    Kombiniert CNN-LSTM-Transformer mit Crossformer und Autoformer Innovationen
    """
    
    def __init__(
        self,
        input_features: int = 50,
        sequence_length: int = 120, 
        d_model: int = 512,
        n_heads: int = 8,
        n_encoder_layers: int = 6,
        n_decoder_layers: int = 6,
        dropout: float = 0.1,
        cnn_filters: int = 128,
        lstm_units: int = 256,
        output_size: int = 1,
        learning_rate: float = 1e-4,
        weight_decay: float = 1e-5,
        use_mixed_precision: bool = True,
        uncertainty_estimation: bool = True,
        ensemble_size: int = 5,
        **kwargs
    ):
        super().__init__()
        self.save_hyperparameters()
        
        self.input_features = input_features
        self.sequence_length = sequence_length
        self.d_model = d_model
        self.use_mixed_precision = use_mixed_precision
        self.uncertainty_estimation = uncertainty_estimation
        self.ensemble_size = ensemble_size
        
        # Feature Engineering Layer
        self.feature_embedding = nn.Linear(input_features, d_model)
        self.positional_encoding = self._generate_positional_encoding(sequence_length, d_model)
        
        # CNN Feature Extractor (Multi-Scale)
        self.conv_blocks = nn.ModuleList([
            self._make_conv_block(input_features, cnn_filters, kernel_size=3),
            self._make_conv_block(input_features, cnn_filters, kernel_size=5), 
            self._make_conv_block(input_features, cnn_filters, kernel_size=7),
        ])
        
        # Feature Fusion
        self.feature_fusion = nn.Linear(cnn_filters * 3, d_model)
        
        # Series Decomposition
        self.decomposition = DecompositionLayer(kernel_size=25)
        
        # LSTM for Sequential Dependencies  
        self.lstm = nn.LSTM(
            d_model, lstm_units, 
            num_layers=2, 
            batch_first=True, 
            dropout=dropout,
            bidirectional=True
        )
        self.lstm_proj = nn.Linear(lstm_units * 2, d_model)
        
        # Cross-Dimension Attention Layers
        self.cross_attention_layers = nn.ModuleList([
            CrossDimensionAttention(d_model, n_heads, dropout) 
            for _ in range(n_encoder_layers)
        ])
        
        # Auto-Correlation Layers
        self.auto_correlation_layers = nn.ModuleList([
            AutoCorrelationMechanism(factor=1, attention_dropout=dropout)
            for _ in range(n_encoder_layers)
        ])
        
        # Layer Normalization
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(d_model) for _ in range(n_encoder_layers * 2)
        ])
        
        # Feed Forward Networks
        self.feed_forwards = nn.ModuleList([
            self._make_ffn(d_model, d_model * 4, dropout)
            for _ in range(n_encoder_layers)
        ])
        
        # Uncertainty Quantification (Bayesian Layers)
        if uncertainty_estimation:
            self.bayesian_layers = nn.ModuleList([
                self._make_bayesian_layer(d_model, d_model)
                for _ in range(ensemble_size)
            ])
        
        # Multi-Head Output Layers (für verschiedene Zeiträume)
        self.output_heads = nn.ModuleDict({
            'short_term': nn.Linear(d_model, output_size),    # 1-5 Tage
            'medium_term': nn.Linear(d_model, output_size),   # 1-4 Wochen  
            'long_term': nn.Linear(d_model, output_size),     # 1-3 Monate
        })
        
        # Advanced Regularization
        self.dropout = nn.Dropout(dropout)
        self.layer_dropout = nn.Dropout(0.05)  # Stochastic Depth
        
        # Metrics
        self.train_mse = MeanSquaredError()
        self.val_mse = MeanSquaredError()
        self.train_mae = MeanAbsoluteError()
        self.val_mae = MeanAbsoluteError()
        
        # Mixed Precision Scaler (only on GPU)
        if use_mixed_precision and torch.cuda.is_available():
            self.automatic_optimization = False
            self.scaler = GradScaler('cuda')
        else:
            self.use_mixed_precision = False  # Disable if no GPU available

    def _make_conv_block(self, in_channels, out_channels, kernel_size):
        return nn.Sequential(
            nn.Conv1d(in_channels, out_channels, kernel_size, padding=kernel_size//2),
            nn.BatchNorm1d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv1d(out_channels, out_channels, kernel_size, padding=kernel_size//2),
            nn.BatchNorm1d(out_channels),  
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool1d(1)
        )
    
    def _make_ffn(self, d_model, d_ff, dropout):
        return nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_ff, d_model),
            nn.Dropout(dropout)
        )
    
    def _make_bayesian_layer(self, input_dim, output_dim):
        """Bayesian Linear Layer für Uncertainty Quantification"""
        return nn.Sequential(
            nn.Linear(input_dim, output_dim),
            nn.Dropout(0.1),  # MC Dropout für approximative Bayesian Inferenz
        )
        
    def _generate_positional_encoding(self, sequence_length, d_model):
        pos_encoding = torch.zeros(sequence_length, d_model)
        position = torch.arange(0, sequence_length, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-np.log(10000.0) / d_model))
        
        pos_encoding[:, 0::2] = torch.sin(position * div_term)
        pos_encoding[:, 1::2] = torch.cos(position * div_term)
        
        return nn.Parameter(pos_encoding.unsqueeze(0), requires_grad=False)

    def forward(self, x, return_attention=False, return_uncertainty=False):
        batch_size, seq_len, features = x.size()
        
        # Multi-Scale CNN Feature Extraction
        cnn_features = []
        x_transposed = x.transpose(1, 2)  # (batch, features, seq_len)
        
        for conv_block in self.conv_blocks:
            conv_out = conv_block(x_transposed)  # (batch, filters, 1)
            cnn_features.append(conv_out.squeeze(-1))
        
        # Feature Fusion
        cnn_fused = torch.cat(cnn_features, dim=-1)  # (batch, filters*3)
        cnn_embedded = self.feature_fusion(cnn_fused).unsqueeze(1)  # (batch, 1, d_model)
        
        # Input Embedding mit Positional Encoding
        embedded = self.feature_embedding(x)  # (batch, seq_len, d_model)
        embedded = embedded + self.positional_encoding[:, :seq_len, :]
        
        # Series Decomposition
        seasonal, trend = self.decomposition(embedded)
        
        # LSTM Processing
        lstm_out, _ = self.lstm(seasonal)
        lstm_projected = self.lstm_proj(lstm_out)
        
        # Kombiniere CNN und LSTM Features
        combined_features = lstm_projected + cnn_embedded.repeat(1, seq_len, 1)
        
        # Transformer Encoder Layers
        attention_weights = []
        hidden = combined_features
        
        for i, (cross_attn, auto_corr, ln1, ln2, ffn) in enumerate(
            zip(self.cross_attention_layers, self.auto_correlation_layers, 
                self.layer_norms[::2], self.layer_norms[1::2], self.feed_forwards)
        ):
            # Cross-Dimension Attention
            attn_out, attn_weights = cross_attn(hidden, hidden, hidden)
            hidden = ln1(hidden + self.layer_dropout(attn_out))
            
            # Auto-Correlation  
            auto_out, corr = auto_corr(
                hidden.unsqueeze(-1), hidden.unsqueeze(-1), hidden.unsqueeze(-1)
            )
            hidden = hidden + self.layer_dropout(auto_out.squeeze(-1))
            
            # Feed Forward
            ffn_out = ffn(hidden)
            hidden = ln2(hidden + self.layer_dropout(ffn_out))
            
            if return_attention:
                attention_weights.append(attn_weights)
        
        # Global Average Pooling
        pooled = torch.mean(hidden, dim=1)  # (batch, d_model)
        
        # Multi-Head Predictions
        outputs = {}
        for head_name, head_layer in self.output_heads.items():
            outputs[head_name] = head_layer(pooled)
        
        # Uncertainty Estimation
        if return_uncertainty and self.uncertainty_estimation:
            predictions = []
            for bayesian_layer in self.bayesian_layers:
                pred = bayesian_layer(pooled)
                predictions.append(pred)
            
            ensemble_mean = torch.stack(predictions).mean(dim=0)
            ensemble_std = torch.stack(predictions).std(dim=0)
            
            outputs['ensemble_mean'] = ensemble_mean
            outputs['epistemic_uncertainty'] = ensemble_std
            outputs['predictions'] = predictions
        
        if return_attention:
            outputs['attention_weights'] = attention_weights
            
        return outputs

    def training_step(self, batch, batch_idx):
        x, y = batch

        # Ensure y has correct shape for loss computation
        if len(y.shape) == 1:
            y = y.unsqueeze(1)  # [batch] -> [batch, 1]

        if self.use_mixed_precision:
            optimizer = self.optimizers()
            optimizer.zero_grad()

            with autocast('cuda'):
                outputs = self(x)
                # Multi-task Loss (gewichtete Kombination verschiedener Zeiträume)
                loss = (
                    0.5 * F.mse_loss(outputs['short_term'], y) +
                    0.3 * F.mse_loss(outputs['medium_term'], y) +
                    0.2 * F.mse_loss(outputs['long_term'], y)
                )

                # Uncertainty Regularization
                if self.uncertainty_estimation and 'epistemic_uncertainty' in outputs:
                    uncertainty_reg = torch.mean(outputs['epistemic_uncertainty'])
                    loss = loss + 0.01 * uncertainty_reg

            self.scaler.scale(loss).backward()
            self.scaler.step(optimizer)
            self.scaler.update()
        else:
            outputs = self(x)
            loss = F.mse_loss(outputs['short_term'], y)

        # Metrics - squeeze outputs to match target shape for metrics
        pred_squeezed = outputs['short_term'].squeeze()
        target_squeezed = y.squeeze()

        self.train_mse(pred_squeezed, target_squeezed)
        self.train_mae(pred_squeezed, target_squeezed)

        self.log('train_loss', loss, prog_bar=True)
        self.log('train_mse', self.train_mse, prog_bar=True)
        self.log('train_mae', self.train_mae)

        return loss

    def validation_step(self, batch, batch_idx):
        x, y = batch

        # Ensure y has correct shape for loss computation
        if len(y.shape) == 1:
            y = y.unsqueeze(1)  # [batch] -> [batch, 1]

        with torch.no_grad():
            outputs = self(x, return_uncertainty=True)
            loss = F.mse_loss(outputs['short_term'], y)

            # Metrics - squeeze outputs to match target shape for metrics
            pred_squeezed = outputs['short_term'].squeeze()
            target_squeezed = y.squeeze()

            self.val_mse(pred_squeezed, target_squeezed)
            self.val_mae(pred_squeezed, target_squeezed)

            self.log('val_loss', loss, prog_bar=True)
            self.log('val_mse', self.val_mse, prog_bar=True)
            self.log('val_mae', self.val_mae)

            # Log Uncertainty Metrics
            if 'epistemic_uncertainty' in outputs:
                mean_uncertainty = torch.mean(outputs['epistemic_uncertainty'])
                self.log('val_uncertainty', mean_uncertainty)

        return loss

    def configure_optimizers(self):
        optimizer = torch.optim.AdamW(
            self.parameters(), 
            lr=self.hparams.learning_rate,
            weight_decay=self.hparams.weight_decay
        )
        
        scheduler = OneCycleLR(
            optimizer,
            max_lr=self.hparams.learning_rate * 10,
            total_steps=self.trainer.estimated_stepping_batches,
            pct_start=0.1,
            anneal_strategy='cos'
        )
        
        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "interval": "step",
            },
        }

    def predict_with_uncertainty(self, x, n_samples=100):
        """
        Monte Carlo Dropout für Uncertainty Quantification
        """
        self.train()  # Aktiviere Dropout
        predictions = []
        
        with torch.no_grad():
            for _ in range(n_samples):
                outputs = self(x, return_uncertainty=True)
                predictions.append(outputs['short_term'])
        
        predictions = torch.stack(predictions)
        mean_pred = predictions.mean(dim=0)
        std_pred = predictions.std(dim=0)
        
        # Confidence Intervals
        lower_bound = mean_pred - 1.96 * std_pred  # 95% CI
        upper_bound = mean_pred + 1.96 * std_pred
        
        return {
            'prediction': mean_pred,
            'epistemic_uncertainty': std_pred,
            'confidence_interval_95': (lower_bound, upper_bound),
            'all_predictions': predictions
        }


class AdvancedFeatureEngineering:
    """
    Hochmoderne Feature Engineering Pipeline für Finanzdaten
    Implementiert State-of-the-Art Techniken aus der Quantitative Finance
    """
    
    def __init__(self):
        self.feature_generators = {
            'technical': self._technical_indicators,
            'microstructure': self._market_microstructure, 
            'alternative': self._alternative_data_features,
            'macro': self._macroeconomic_features,
            'cross_asset': self._cross_asset_features,
            'temporal': self._temporal_features
        }
    
    def _technical_indicators(self, df):
        """Advanced Technical Indicators"""
        features = pd.DataFrame(index=df.index)
        
        # Multi-timeframe Moving Averages
        for period in [5, 10, 20, 50, 100, 200]:
            features[f'sma_{period}'] = df['close'].rolling(period).mean()
            features[f'ema_{period}'] = df['close'].ewm(span=period).mean()
            
        # Bollinger Bands with multiple standard deviations
        for std_dev in [1.5, 2.0, 2.5]:
            rolling_mean = df['close'].rolling(20).mean()
            rolling_std = df['close'].rolling(20).std()
            features[f'bb_upper_{std_dev}'] = rolling_mean + (rolling_std * std_dev)
            features[f'bb_lower_{std_dev}'] = rolling_mean - (rolling_std * std_dev)
            features[f'bb_width_{std_dev}'] = features[f'bb_upper_{std_dev}'] - features[f'bb_lower_{std_dev}']
        
        # Advanced Momentum Indicators
        features['rsi'] = self._calculate_rsi(df['close'])
        features['stoch_k'], features['stoch_d'] = self._calculate_stochastic(df)
        features['williams_r'] = self._calculate_williams_r(df)
        
        # MACD Family
        features['macd'], features['macd_signal'], features['macd_histogram'] = self._calculate_macd(df['close'])
        
        # Volatility Indicators
        features['atr'] = self._calculate_atr(df)
        features['garch_volatility'] = self._calculate_garch_volatility(df['close'])
        
        return features
    
    def _market_microstructure(self, df):
        """Market Microstructure Features"""
        features = pd.DataFrame(index=df.index)
        
        # Order Flow Imbalance
        features['bid_ask_spread'] = (df['ask'] - df['bid']) / df['close']
        features['order_flow_imbalance'] = (df['buy_volume'] - df['sell_volume']) / df['volume']
        
        # Volume Profile
        features['volume_weighted_price'] = (df['volume'] * df['close']).cumsum() / df['volume'].cumsum()
        features['relative_volume'] = df['volume'] / df['volume'].rolling(20).mean()
        
        # Price Impact Measures
        features['price_impact'] = df['close'].pct_change() / np.log(df['volume'] + 1)
        
        return features
    
    def _alternative_data_features(self, df, news_sentiment=None, social_sentiment=None):
        """Alternative Data Integration"""
        features = pd.DataFrame(index=df.index)
        
        if news_sentiment is not None:
            features['news_sentiment'] = news_sentiment
            features['news_sentiment_ma'] = news_sentiment.rolling(5).mean()
        
        if social_sentiment is not None:
            features['social_sentiment'] = social_sentiment
            features['social_buzz'] = social_sentiment.rolling(3).std()
            
        return features
    
    def _cross_asset_features(self, df, market_indices=None):
        """Cross-Asset Features"""
        features = pd.DataFrame(index=df.index)
        
        if market_indices is not None:
            for name, index_data in market_indices.items():
                # Beta calculation
                returns = df['close'].pct_change()
                index_returns = index_data.pct_change()
                
                rolling_beta = returns.rolling(60).corr(index_returns)
                features[f'beta_{name}'] = rolling_beta
                
                # Relative Strength
                features[f'relative_strength_{name}'] = (returns.rolling(20).mean() / 
                                                       index_returns.rolling(20).mean())
        
        return features
    
    def generate_features(self, df, **kwargs):
        """Hauptmethode für Feature-Generierung"""
        all_features = [df[['open', 'high', 'low', 'close', 'volume']]]
        
        for feature_type, generator in self.feature_generators.items():
            try:
                if feature_type in ['alternative', 'cross_asset']:
                    features = generator(df, **kwargs)
                else:
                    features = generator(df)
                all_features.append(features)
            except Exception as e:
                print(f"Warnung: Fehler bei {feature_type} Features: {e}")
                
        combined_features = pd.concat(all_features, axis=1)
        
        # Feature Scaling und Normalization
        from sklearn.preprocessing import RobustScaler
        scaler = RobustScaler()
        scaled_features = scaler.fit_transform(combined_features.fillna(0))
        
        return pd.DataFrame(scaled_features, 
                          index=combined_features.index,
                          columns=combined_features.columns), scaler


class ProductionTrainingPipeline:
    """
    Enterprise-Grade Training Pipeline mit MLflow Integration
    """
    
    def __init__(self, mlflow_tracking_uri="http://localhost:5000"):
        mlflow.set_tracking_uri(mlflow_tracking_uri)
        self.experiment_name = "V5_Trader_Advanced_Models"
        
        try:
            mlflow.create_experiment(self.experiment_name)
        except:
            pass  # Experiment existiert bereits
            
        mlflow.set_experiment(self.experiment_name)
    
    def train_ensemble(self, data_module, n_models=5, hyperparameter_ranges=None):
        """
        Trainiert Ensemble von Modellen mit automatischer Hyperparameter-Optimierung
        """
        from optuna.integration import PyTorchLightningPruningCallback
        import optuna
        
        def objective(trial):
            # Hyperparameter Sampling
            params = {
                'd_model': trial.suggest_categorical('d_model', [256, 512, 768]),
                'n_heads': trial.suggest_categorical('n_heads', [8, 12, 16]),
                'n_encoder_layers': trial.suggest_int('n_encoder_layers', 4, 8),
                'dropout': trial.suggest_float('dropout', 0.1, 0.3),
                'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-3, log=True),
                'cnn_filters': trial.suggest_categorical('cnn_filters', [64, 128, 256]),
                'lstm_units': trial.suggest_categorical('lstm_units', [128, 256, 512]),
            }
            
            with mlflow.start_run(nested=True):
                # Log Hyperparameters
                mlflow.log_params(params)
                
                # Model Creation
                model = AdvancedFinancialTransformer(**params)
                
                # Callbacks
                callbacks = [
                    pl.callbacks.EarlyStopping(
                        monitor='val_loss',
                        patience=15,
                        verbose=True,
                        mode='min'
                    ),
                    pl.callbacks.ModelCheckpoint(
                        monitor='val_loss',
                        save_top_k=1,
                        mode='min',
                        save_last=True
                    ),
                    pl.callbacks.LearningRateMonitor(logging_interval='step'),
                    PyTorchLightningPruningCallback(trial, monitor="val_loss")
                ]
                
                # Trainer mit Advanced Settings
                trainer = pl.Trainer(
                    max_epochs=100,
                    accelerator='gpu' if torch.cuda.is_available() else 'cpu',
                    devices=1,
                    precision='16-mixed' if torch.cuda.is_available() else 32,
                    callbacks=callbacks,
                    gradient_clip_val=1.0,
                    accumulate_grad_batches=2,
                    deterministic=True,
                    enable_progress_bar=False
                )
                
                # Training
                trainer.fit(model, data_module)
                
                # Log Final Metrics
                val_loss = trainer.callback_metrics['val_loss'].item()
                mlflow.log_metric("final_val_loss", val_loss)
                
                # Log Model
                mlflow.pytorch.log_model(model, "model")
                
                return val_loss
        
        # Optuna Study
        study = optuna.create_study(
            direction='minimize',
            sampler=optuna.samplers.TPESampler(seed=42),
            pruner=optuna.pruners.MedianPruner(n_startup_trials=5, n_warmup_steps=30)
        )
        
        study.optimize(objective, n_trials=50, timeout=3600*6)  # 6 Stunden
        
        # Beste Parameter für Ensemble Training
        best_params = study.best_params
        
        # Ensemble Training
        ensemble_models = []
        for i in range(n_models):
            with mlflow.start_run(run_name=f"ensemble_model_{i}"):
                # Slight parameter variation für Ensemble Diversity
                varied_params = best_params.copy()
                varied_params['dropout'] *= np.random.uniform(0.8, 1.2)
                varied_params['learning_rate'] *= np.random.uniform(0.8, 1.2)
                
                model = AdvancedFinancialTransformer(**varied_params)
                
                trainer = pl.Trainer(
                    max_epochs=150,
                    accelerator='gpu' if torch.cuda.is_available() else 'cpu',
                    devices=1,
                    precision='16-mixed' if torch.cuda.is_available() else 32,
                    callbacks=[
                        pl.callbacks.EarlyStopping(monitor='val_loss', patience=20),
                        pl.callbacks.ModelCheckpoint(monitor='val_loss', save_top_k=1)
                    ]
                )
                
                trainer.fit(model, data_module)
                ensemble_models.append(model)
                
                # Register in MLflow Model Registry
                model_uri = f"runs:/{mlflow.active_run().info.run_id}/model"
                mlflow.register_model(model_uri, f"V5Trader_Ensemble_Model_{i}")
        
        return ensemble_models, study
    
    def deploy_to_production(self, model_name, stage="Production"):
        """
        Produktionsdeployment über MLflow
        """
        client = mlflow.MlflowClient()
        
        # Hole Latest Model Version
        latest_version = client.get_latest_versions(
            model_name, 
            stages=["None"]
        )[0]
        
        # Transition zu Production
        client.transition_model_version_stage(
            name=model_name,
            version=latest_version.version,
            stage=stage,
            archive_existing_versions=True
        )
        
        print(f"Model {model_name} version {latest_version.version} deployed to {stage}")
        
        return latest_version


class RealTimeInferenceEngine:
    """
    Ultra-Low Latency Inference Engine für Production Trading
    """
    
    def __init__(self, model_registry_name, mlflow_uri="http://localhost:5000"):
        self.mlflow_uri = mlflow_uri
        mlflow.set_tracking_uri(mlflow_uri)
        
        # Load Production Models
        self.models = self._load_production_ensemble(model_registry_name)
        
        # Feature Engineering Pipeline
        self.feature_engineer = AdvancedFeatureEngineering()
        
        # Caching für Features
        self.feature_cache = {}
        self.cache_ttl = 60  # Sekunden
        
        # Performance Tracking
        self.inference_times = []
        self.prediction_history = []
        
    def _load_production_ensemble(self, model_name):
        """Lädt Ensemble von Production Models"""
        client = mlflow.MlflowClient()
        models = []
        
        # Suche alle Production Models
        for mv in client.search_model_versions(f"name='{model_name}'"):
            if mv.current_stage == "Production":
                model_uri = f"models:/{model_name}/{mv.version}"
                model = mlflow.pytorch.load_model(model_uri)
                model.eval()
                if torch.cuda.is_available():
                    model = model.cuda()
                models.append(model)
        
        print(f"Loaded {len(models)} production models")
        return models
    
    @torch.inference_mode()
    def predict(self, raw_data, return_uncertainty=True, use_cache=True):
        """
        Ultra-schnelle Vorhersage mit Uncertainty Quantification
        """
        start_time = time.time()
        
        # Feature Engineering mit Caching
        cache_key = self._generate_cache_key(raw_data)
        
        if use_cache and cache_key in self.feature_cache:
            features, cache_time = self.feature_cache[cache_key]
            if time.time() - cache_time < self.cache_ttl:
                processed_features = features
            else:
                processed_features = self._process_features(raw_data)
                self.feature_cache[cache_key] = (processed_features, time.time())
        else:
            processed_features = self._process_features(raw_data)
            if use_cache:
                self.feature_cache[cache_key] = (processed_features, time.time())
        
        # Ensemble Predictions
        predictions = []
        uncertainties = []
        
        with autocast('cuda' if torch.cuda.is_available() else 'cpu'):
            for model in self.models:
                output = model(processed_features, return_uncertainty=return_uncertainty)
                predictions.append(output['short_term'])
                
                if return_uncertainty and 'epistemic_uncertainty' in output:
                    uncertainties.append(output['epistemic_uncertainty'])
        
        # Ensemble Aggregation
        ensemble_prediction = torch.stack(predictions).mean(dim=0)
        
        if uncertainties:
            # Epistemic Uncertainty (Model Disagreement)
            epistemic_uncertainty = torch.stack(predictions).std(dim=0)
            
            # Aleatoric Uncertainty (Data Uncertainty)
            aleatoric_uncertainty = torch.stack(uncertainties).mean(dim=0)
            
            # Total Uncertainty
            total_uncertainty = torch.sqrt(epistemic_uncertainty**2 + aleatoric_uncertainty**2)
        else:
            epistemic_uncertainty = torch.zeros_like(ensemble_prediction)
            aleatoric_uncertainty = torch.zeros_like(ensemble_prediction)
            total_uncertainty = torch.zeros_like(ensemble_prediction)
        
        inference_time = time.time() - start_time
        self.inference_times.append(inference_time)
        
        result = {
            'prediction': ensemble_prediction.cpu().numpy(),
            'epistemic_uncertainty': epistemic_uncertainty.cpu().numpy(),
            'aleatoric_uncertainty': aleatoric_uncertainty.cpu().numpy(), 
            'total_uncertainty': total_uncertainty.cpu().numpy(),
            'confidence_score': self._calculate_confidence_score(total_uncertainty),
            'inference_time_ms': inference_time * 1000,
            'model_count': len(self.models)
        }
        
        self.prediction_history.append({
            'timestamp': datetime.now(),
            'prediction': result['prediction'],
            'uncertainty': result['total_uncertainty'],
            'inference_time': inference_time
        })
        
        return result
    
    def _calculate_confidence_score(self, uncertainty):
        """Berechnet Confidence Score basierend auf Uncertainty"""
        # Invertiere Uncertainty für Confidence (weniger Uncertainty = mehr Confidence)
        confidence = torch.exp(-uncertainty.mean())
        return confidence.cpu().numpy()
    
    def get_performance_metrics(self):
        """Performance Metriken für Monitoring"""
        if not self.inference_times:
            return {}
            
        return {
            'avg_inference_time_ms': np.mean(self.inference_times) * 1000,
            'p95_inference_time_ms': np.percentile(self.inference_times, 95) * 1000,
            'p99_inference_time_ms': np.percentile(self.inference_times, 99) * 1000,
            'total_predictions': len(self.inference_times),
            'cache_hit_rate': len(self.feature_cache) / max(len(self.inference_times), 1)
        }
