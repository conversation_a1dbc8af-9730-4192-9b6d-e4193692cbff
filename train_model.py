#!/usr/bin/env python3
"""
🧠 V5_Trader - Model Training Script
Trainiert das Transformer-Modell mit den verfügbaren Marktdaten
"""

import os
import sys
import pandas as pd
import numpy as np
import torch
import pytorch_lightning as pl
from pathlib import Path
import mlflow
import mlflow.pytorch
from datetime import datetime

# Projekt-Root hinzufügen
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def setup_mlflow():
    """MLflow Setup"""
    mlflow.set_tracking_uri("http://localhost:5000")
    mlflow.set_experiment("V5Trader_Training")
    print("✅ MLflow konfiguriert")

def load_and_prepare_data():
    """Lädt und bereitet die Marktdaten vor"""
    print("📊 Lade Marktdaten...")
    
    # Lade die CSV-Daten
    data_path = project_root / "data" / "market_data.csv"
    if not data_path.exists():
        print("❌ Marktdaten nicht gefunden!")
        return None
    
    df = pd.read_csv(data_path)
    print(f"✅ {len(df)} Datenpunkte geladen")
    print(f"📅 Zeitraum: {df['Date'].min()} bis {df['Date'].max()}")
    
    # Basis-Features erstellen
    df['Date'] = pd.to_datetime(df['Date'])
    df = df.sort_values('Date')
    
    # Technische Indikatoren
    df['Returns'] = df['Close'].pct_change()
    df['SMA_5'] = df['Close'].rolling(5).mean()
    df['SMA_20'] = df['Close'].rolling(20).mean()
    df['Volatility'] = df['Returns'].rolling(20).std()
    
    # RSI
    delta = df['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['RSI'] = 100 - (100 / (1 + rs))
    
    # Target: Nächster Tag steigt (1) oder fällt (0)
    df['Target'] = (df['Close'].shift(-1) > df['Close']).astype(int)
    
    # Entferne NaN-Werte
    df = df.dropna()
    
    print(f"✅ {len(df)} bereinigte Datenpunkte")
    return df

def create_sequences(df, sequence_length=20):
    """Erstellt Sequenzen für das Transformer-Modell"""
    print(f"🔄 Erstelle Sequenzen (Länge: {sequence_length})...")
    
    # Feature-Spalten
    feature_cols = ['Open', 'High', 'Low', 'Close', 'Volume', 
                   'Returns', 'SMA_5', 'SMA_20', 'Volatility', 'RSI']
    
    # Normalisierung
    features = df[feature_cols].values
    features = (features - features.mean(axis=0)) / features.std(axis=0)
    
    targets = df['Target'].values
    
    # Sequenzen erstellen
    X, y = [], []
    for i in range(sequence_length, len(features)):
        X.append(features[i-sequence_length:i])
        y.append(targets[i])
    
    X = np.array(X)
    y = np.array(y)
    
    print(f"✅ {len(X)} Sequenzen erstellt")
    print(f"📊 Input Shape: {X.shape}")
    print(f"🎯 Target Shape: {y.shape}")
    
    return X, y, feature_cols

class SimpleTransformer(pl.LightningModule):
    """Einfaches Transformer-Modell für Trading"""
    
    def __init__(self, input_dim=10, d_model=128, n_heads=8, n_layers=4, 
                 dropout=0.1, learning_rate=1e-4):
        super().__init__()
        self.save_hyperparameters()
        
        # Input Projection
        self.input_projection = torch.nn.Linear(input_dim, d_model)
        
        # Positional Encoding
        self.pos_encoding = torch.nn.Parameter(torch.randn(1000, d_model))
        
        # Transformer Encoder
        encoder_layer = torch.nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dropout=dropout,
            batch_first=True
        )
        self.transformer = torch.nn.TransformerEncoder(encoder_layer, n_layers)
        
        # Output Head
        self.classifier = torch.nn.Sequential(
            torch.nn.Linear(d_model, 64),
            torch.nn.ReLU(),
            torch.nn.Dropout(dropout),
            torch.nn.Linear(64, 1),
            torch.nn.Sigmoid()
        )
        
        self.criterion = torch.nn.BCELoss()
        
    def forward(self, x):
        # x: (batch, seq_len, features)
        batch_size, seq_len, _ = x.shape
        
        # Input projection
        x = self.input_projection(x)
        
        # Add positional encoding
        x = x + self.pos_encoding[:seq_len].unsqueeze(0)
        
        # Transformer
        x = self.transformer(x)
        
        # Global average pooling
        x = x.mean(dim=1)
        
        # Classification
        return self.classifier(x).squeeze()
    
    def training_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self(x)
        loss = self.criterion(y_hat, y.float())
        
        # Accuracy
        pred = (y_hat > 0.5).float()
        acc = (pred == y.float()).float().mean()
        
        self.log('train_loss', loss, prog_bar=True)
        self.log('train_acc', acc, prog_bar=True)
        
        return loss
    
    def validation_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self(x)
        loss = self.criterion(y_hat, y.float())
        
        # Accuracy
        pred = (y_hat > 0.5).float()
        acc = (pred == y.float()).float().mean()
        
        self.log('val_loss', loss, prog_bar=True)
        self.log('val_acc', acc, prog_bar=True)
        
        return loss
    
    def configure_optimizers(self):
        return torch.optim.AdamW(self.parameters(), lr=self.hparams.learning_rate)

def train_model():
    """Haupttraining-Funktion"""
    print("🚀 Starte Model Training...")
    
    # MLflow Setup
    setup_mlflow()
    
    # Daten laden
    df = load_and_prepare_data()
    if df is None:
        return
    
    # Sequenzen erstellen
    X, y, feature_cols = create_sequences(df)
    
    # Train/Val Split
    split_idx = int(0.8 * len(X))
    X_train, X_val = X[:split_idx], X[split_idx:]
    y_train, y_val = y[:split_idx], y[split_idx:]
    
    print(f"📊 Training: {len(X_train)} samples")
    print(f"📊 Validation: {len(X_val)} samples")
    
    # DataLoaders
    train_dataset = torch.utils.data.TensorDataset(
        torch.FloatTensor(X_train), 
        torch.LongTensor(y_train)
    )
    val_dataset = torch.utils.data.TensorDataset(
        torch.FloatTensor(X_val), 
        torch.LongTensor(y_val)
    )
    
    train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=32)
    
    # Model
    model = SimpleTransformer(
        input_dim=len(feature_cols),
        d_model=128,
        n_heads=8,
        n_layers=4,
        dropout=0.1,
        learning_rate=1e-4
    )
    
    # Trainer
    trainer = pl.Trainer(
        max_epochs=50,
        accelerator='auto',
        devices=1,
        logger=pl.loggers.MLFlowLogger(
            experiment_name="V5Trader_Training",
            tracking_uri="http://localhost:5000"
        ),
        callbacks=[
            pl.callbacks.EarlyStopping(monitor='val_loss', patience=10),
            pl.callbacks.ModelCheckpoint(
                dirpath='models/checkpoints',
                filename='v5trader-{epoch:02d}-{val_acc:.3f}',
                monitor='val_acc',
                mode='max',
                save_top_k=3
            )
        ]
    )
    
    # Training starten
    print("🏋️ Starte Training...")
    with mlflow.start_run():
        # Log parameters
        mlflow.log_params({
            'model_type': 'SimpleTransformer',
            'sequence_length': 20,
            'd_model': 128,
            'n_heads': 8,
            'n_layers': 4,
            'learning_rate': 1e-4,
            'train_samples': len(X_train),
            'val_samples': len(X_val),
            'features': feature_cols
        })
        
        trainer.fit(model, train_loader, val_loader)
        
        # Save final model
        model_path = "models/v5trader_transformer.pth"
        torch.save(model.state_dict(), model_path)
        mlflow.log_artifact(model_path)
        
        print("✅ Training abgeschlossen!")
        print(f"💾 Modell gespeichert: {model_path}")
        print("📊 MLflow UI: http://localhost:5000")

if __name__ == "__main__":
    train_model()
