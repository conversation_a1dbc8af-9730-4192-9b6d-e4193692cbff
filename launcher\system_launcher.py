#!/usr/bin/env python3
"""
Modular system launcher for V5_Trader
Clean separation of concerns with proper CLI interface
"""
import click
import logging
import sys
from pathlib import Path
from typing import List, Optional

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from launcher.checks import SystemChecker
from launcher.services import ServiceManager
from launcher.monitoring import MonitoringManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class V5TraderLauncher:
    """Main system launcher with modular components"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.absolute()
        self.checker = SystemChecker(self.project_root)
        self.service_manager = ServiceManager(self.project_root)
        self.monitoring_manager = MonitoringManager(self.project_root)
    
    def check_system(self) -> bool:
        """Run comprehensive system checks"""
        logger.info("🔍 Running system checks...")
        return self.checker.run_all_checks()
    
    def start_services(self, services: Optional[List[str]] = None) -> bool:
        """Start specified services or all services"""
        logger.info("🚀 Starting services...")
        return self.service_manager.start_services(services)
    
    def stop_services(self) -> bool:
        """Stop all running services"""
        logger.info("🛑 Stopping services...")
        return self.service_manager.stop_services()
    
    def start_monitoring(self) -> bool:
        """Start monitoring stack"""
        logger.info("📊 Starting monitoring...")
        return self.monitoring_manager.start_monitoring()
    
    def get_status(self) -> dict:
        """Get system status"""
        return {
            "services": self.service_manager.get_service_status(),
            "monitoring": self.monitoring_manager.get_monitoring_status(),
            "health": self.checker.get_health_summary()
        }

@click.group()
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
def cli(verbose):
    """V5_Trader System Launcher"""
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)

@cli.command()
@click.option('--skip-checks', is_flag=True, help='Skip system checks')
@click.option('--services', '-s', multiple=True, help='Specific services to start')
@click.option('--monitoring/--no-monitoring', default=True, help='Start monitoring stack')
def start(skip_checks, services, monitoring):
    """Start the V5_Trader system"""
    launcher = V5TraderLauncher()
    
    try:
        # System checks
        if not skip_checks:
            if not launcher.check_system():
                logger.error("❌ System checks failed!")
                sys.exit(1)
        
        # Start services
        service_list = list(services) if services else None
        if not launcher.start_services(service_list):
            logger.error("❌ Failed to start services!")
            sys.exit(1)
        
        # Start monitoring
        if monitoring:
            if not launcher.start_monitoring():
                logger.warning("⚠️ Monitoring failed to start")
        
        logger.info("✅ V5_Trader system started successfully!")
        
        # Display status
        status = launcher.get_status()
        click.echo("\n📊 System Status:")
        for category, items in status.items():
            click.echo(f"  {category.title()}:")
            for key, value in items.items():
                status_icon = "✅" if value else "❌"
                click.echo(f"    {status_icon} {key}")
        
    except KeyboardInterrupt:
        logger.info("🛑 Startup interrupted by user")
        launcher.stop_services()
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Startup failed: {e}")
        sys.exit(1)

@cli.command()
def stop():
    """Stop the V5_Trader system"""
    launcher = V5TraderLauncher()
    
    try:
        if launcher.stop_services():
            logger.info("✅ V5_Trader system stopped successfully!")
        else:
            logger.error("❌ Failed to stop some services")
            sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Stop failed: {e}")
        sys.exit(1)

@cli.command()
def status():
    """Show system status"""
    launcher = V5TraderLauncher()
    
    try:
        status = launcher.get_status()
        
        click.echo("🚀 V5_Trader System Status")
        click.echo("=" * 40)
        
        for category, items in status.items():
            click.echo(f"\n📊 {category.title()}:")
            for key, value in items.items():
                if isinstance(value, bool):
                    status_icon = "✅" if value else "❌"
                    click.echo(f"  {status_icon} {key}")
                else:
                    click.echo(f"  📈 {key}: {value}")
        
    except Exception as e:
        logger.error(f"❌ Status check failed: {e}")
        sys.exit(1)

@cli.command()
@click.option('--fix', is_flag=True, help='Attempt to fix issues automatically')
def check():
    """Run system health checks"""
    launcher = V5TraderLauncher()
    
    try:
        if launcher.check_system():
            click.echo("✅ All system checks passed!")
        else:
            click.echo("❌ Some system checks failed!")
            if not fix:
                click.echo("💡 Run with --fix to attempt automatic fixes")
            sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Health check failed: {e}")
        sys.exit(1)

@cli.command()
@click.argument('service_name')
@click.argument('action', type=click.Choice(['start', 'stop', 'restart', 'status']))
def service(service_name, action):
    """Manage individual services"""
    launcher = V5TraderLauncher()
    
    try:
        if action == 'start':
            success = launcher.start_services([service_name])
        elif action == 'stop':
            success = launcher.service_manager.stop_service(service_name)
        elif action == 'restart':
            launcher.service_manager.stop_service(service_name)
            success = launcher.start_services([service_name])
        elif action == 'status':
            status = launcher.service_manager.get_service_status()
            service_status = status.get(service_name, "Unknown")
            click.echo(f"{service_name}: {service_status}")
            return
        
        if success:
            click.echo(f"✅ Service {service_name} {action} successful!")
        else:
            click.echo(f"❌ Service {service_name} {action} failed!")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ Service {action} failed: {e}")
        sys.exit(1)

@cli.command()
def monitor():
    """Start monitoring mode (keeps system running)"""
    launcher = V5TraderLauncher()
    
    try:
        # Start system if not running
        if not launcher.start_services():
            logger.error("❌ Failed to start services!")
            sys.exit(1)
        
        if not launcher.start_monitoring():
            logger.warning("⚠️ Monitoring failed to start")
        
        logger.info("🔍 Monitoring mode started. Press Ctrl+C to stop.")
        
        # Keep running and monitor
        import time
        while True:
            time.sleep(30)
            status = launcher.get_status()
            
            # Check for failed services
            failed_services = [
                name for name, status in status["services"].items() 
                if not status
            ]
            
            if failed_services:
                logger.warning(f"⚠️ Failed services detected: {failed_services}")
        
    except KeyboardInterrupt:
        logger.info("🛑 Monitoring stopped by user")
        launcher.stop_services()
    except Exception as e:
        logger.error(f"❌ Monitoring failed: {e}")
        launcher.stop_services()
        sys.exit(1)

@cli.command()
@click.option('--config', '-c', help='Configuration file path')
def train():
    """Train AI models"""
    try:
        from train_model_clean import ModelTrainer
        
        trainer = ModelTrainer(config)
        run_id = trainer.train()
        
        click.echo(f"✅ Training completed! MLflow Run ID: {run_id}")
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        sys.exit(1)

@cli.command()
def test():
    """Run system tests"""
    try:
        from tests.test_integration_consolidated import run_integration_tests
        
        if run_integration_tests():
            click.echo("✅ All tests passed!")
        else:
            click.echo("❌ Some tests failed!")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ Testing failed: {e}")
        sys.exit(1)

@cli.command()
@click.option('--type', 'maintenance_type', 
              type=click.Choice(['backup', 'cleanup', 'update', 'full']),
              default='full', help='Type of maintenance to perform')
def maintenance(maintenance_type):
    """Run system maintenance"""
    try:
        from tools.maintenance import V5TraderMaintenance
        
        maintenance = V5TraderMaintenance()
        
        if maintenance_type == 'backup':
            maintenance.backup_system()
        elif maintenance_type == 'cleanup':
            maintenance.cleanup_logs()
            maintenance.cleanup_backups()
        elif maintenance_type == 'update':
            maintenance.update_dependencies()
        elif maintenance_type == 'full':
            maintenance.run_full_maintenance()
        
        click.echo(f"✅ {maintenance_type.title()} maintenance completed!")
        
    except Exception as e:
        logger.error(f"❌ Maintenance failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    cli()
