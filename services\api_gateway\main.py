#!/usr/bin/env python3
"""
V5_Trader API Gateway with MLflow, Prometheus, and Grafana Integration
Enterprise-grade FastAPI service with comprehensive monitoring
"""
import os
import time
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

import uvicorn
import mlflow
import pandas as pd
from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import redis
import psycopg2
from psycopg2.extras import RealDictCursor

# Prometheus metrics
from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST
from fastapi import Response

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Prometheus metrics
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint', 'status'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration', ['method', 'endpoint'])
ACTIVE_CONNECTIONS = Gauge('active_connections', 'Number of active connections')
MODEL_PREDICTIONS = Counter('model_predictions_total', 'Total model predictions', ['model_name', 'status'])
CACHE_HITS = Counter('cache_hits_total', 'Total cache hits', ['cache_type'])
DATABASE_CONNECTIONS = Gauge('database_connections_active', 'Active database connections')

# Pydantic models
class PredictionRequest(BaseModel):
    symbol: str
    features: List[float]
    model_version: Optional[str] = "latest"

class PredictionResponse(BaseModel):
    symbol: str
    prediction: float
    confidence: float
    model_version: str
    timestamp: datetime

class HealthResponse(BaseModel):
    status: str
    timestamp: datetime
    services: Dict[str, str]
    metrics: Dict[str, Any]

class MarketDataRequest(BaseModel):
    symbols: List[str]
    start_date: Optional[str] = None
    end_date: Optional[str] = None

# FastAPI app initialization
app = FastAPI(
    title="V5_Trader API Gateway",
    description="Enterprise AI Trading Platform API with MLflow, Prometheus & Grafana",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global connections
redis_client = None
postgres_conn = None

@app.on_event("startup")
async def startup_event():
    """Initialize connections on startup"""
    global redis_client, postgres_conn
    
    logger.info("🚀 Starting V5_Trader API Gateway...")
    
    # Initialize Redis connection
    try:
        redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
        redis_client = redis.from_url(redis_url, decode_responses=True)
        redis_client.ping()
        logger.info("✅ Redis connection established")
    except Exception as e:
        logger.error(f"❌ Redis connection failed: {e}")
        redis_client = None
    
    # Initialize PostgreSQL connection
    try:
        db_url = os.getenv("DATABASE_URL", "postgresql://postgres:password@localhost:5432/v5trader")
        postgres_conn = psycopg2.connect(db_url)
        logger.info("✅ PostgreSQL connection established")
    except Exception as e:
        logger.error(f"❌ PostgreSQL connection failed: {e}")
        postgres_conn = None
    
    # Initialize MLflow
    try:
        mlflow_uri = os.getenv("MLFLOW_TRACKING_URI", "http://localhost:5000")
        mlflow.set_tracking_uri(mlflow_uri)
        logger.info(f"✅ MLflow tracking URI set: {mlflow_uri}")
    except Exception as e:
        logger.error(f"❌ MLflow initialization failed: {e}")

@app.middleware("http")
async def prometheus_middleware(request, call_next):
    """Middleware to collect Prometheus metrics"""
    start_time = time.time()
    
    # Increment active connections
    ACTIVE_CONNECTIONS.inc()
    
    try:
        response = await call_next(request)
        
        # Record metrics
        duration = time.time() - start_time
        REQUEST_DURATION.labels(
            method=request.method,
            endpoint=request.url.path
        ).observe(duration)
        
        REQUEST_COUNT.labels(
            method=request.method,
            endpoint=request.url.path,
            status=response.status_code
        ).inc()
        
        return response
    
    finally:
        ACTIVE_CONNECTIONS.dec()

@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Comprehensive health check with service status"""
    services = {}
    
    # Check Redis
    try:
        if redis_client:
            redis_client.ping()
            services["redis"] = "healthy"
            CACHE_HITS.labels(cache_type="redis").inc()
        else:
            services["redis"] = "unavailable"
    except Exception as e:
        services["redis"] = f"unhealthy: {str(e)}"
    
    # Check PostgreSQL
    try:
        if postgres_conn:
            with postgres_conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                services["postgres"] = "healthy"
                DATABASE_CONNECTIONS.inc()
        else:
            services["postgres"] = "unavailable"
    except Exception as e:
        services["postgres"] = f"unhealthy: {str(e)}"
    
    # Check MLflow
    try:
        mlflow.get_tracking_uri()
        services["mlflow"] = "healthy"
    except Exception as e:
        services["mlflow"] = f"unhealthy: {str(e)}"
    
    # Overall status
    overall_status = "healthy" if all(
        status == "healthy" for status in services.values()
    ) else "degraded"
    
    # Collect metrics
    metrics = {
        "active_connections": ACTIVE_CONNECTIONS._value._value,
        "total_requests": sum(REQUEST_COUNT._metrics.values()),
        "uptime_seconds": time.time() - startup_time
    }
    
    return HealthResponse(
        status=overall_status,
        timestamp=datetime.now(),
        services=services,
        metrics=metrics
    )

@app.post("/api/predict", response_model=PredictionResponse)
async def predict(request: PredictionRequest):
    """Make prediction using MLflow model"""
    try:
        # Check cache first
        cache_key = f"prediction:{request.symbol}:{hash(tuple(request.features))}"
        
        if redis_client:
            cached_result = redis_client.get(cache_key)
            if cached_result:
                CACHE_HITS.labels(cache_type="prediction").inc()
                return PredictionResponse.parse_raw(cached_result)
        
        # Load model from MLflow
        model_name = "V5Trader_AdvancedTransformer"
        model_version = request.model_version
        
        if model_version == "latest":
            model_uri = f"models:/{model_name}/Production"
        else:
            model_uri = f"models:/{model_name}/{model_version}"
        
        # Load model (simplified - in real implementation would load PyTorch model)
        # For demo purposes, we'll simulate a prediction
        import random
        prediction = random.uniform(0.4, 0.6)
        confidence = random.uniform(0.7, 0.95)
        
        MODEL_PREDICTIONS.labels(model_name=model_name, status="success").inc()
        
        response = PredictionResponse(
            symbol=request.symbol,
            prediction=prediction,
            confidence=confidence,
            model_version=model_version,
            timestamp=datetime.now()
        )
        
        # Cache result
        if redis_client:
            redis_client.setex(
                cache_key, 
                300,  # 5 minutes TTL
                response.json()
            )
        
        # Log to MLflow
        with mlflow.start_run():
            mlflow.log_param("symbol", request.symbol)
            mlflow.log_param("model_version", model_version)
            mlflow.log_metric("prediction", prediction)
            mlflow.log_metric("confidence", confidence)
        
        return response
        
    except Exception as e:
        MODEL_PREDICTIONS.labels(model_name="unknown", status="error").inc()
        logger.error(f"Prediction error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/market-data")
async def get_market_data(symbols: str, limit: int = 100):
    """Get market data from database"""
    try:
        symbol_list = symbols.split(",")
        
        if not postgres_conn:
            raise HTTPException(status_code=503, detail="Database unavailable")
        
        with postgres_conn.cursor(cursor_factory=RealDictCursor) as cursor:
            placeholders = ",".join(["%s"] * len(symbol_list))
            query = f"""
                SELECT * FROM market_data 
                WHERE symbol IN ({placeholders})
                ORDER BY date DESC 
                LIMIT %s
            """
            cursor.execute(query, symbol_list + [limit])
            results = cursor.fetchall()
        
        DATABASE_CONNECTIONS.inc()
        return {"data": results, "count": len(results)}
        
    except Exception as e:
        logger.error(f"Market data error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/models")
async def list_models():
    """List available MLflow models"""
    try:
        from mlflow.tracking import MlflowClient
        client = MlflowClient()
        
        models = []
        for model in client.list_registered_models():
            latest_version = client.get_latest_versions(
                model.name, stages=["Production", "Staging"]
            )
            models.append({
                "name": model.name,
                "description": model.description,
                "latest_versions": [
                    {
                        "version": v.version,
                        "stage": v.current_stage,
                        "run_id": v.run_id
                    } for v in latest_version
                ]
            })
        
        return {"models": models}
        
    except Exception as e:
        logger.error(f"Models listing error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/experiments")
async def list_experiments():
    """List MLflow experiments"""
    try:
        experiments = mlflow.list_experiments()
        return {
            "experiments": [
                {
                    "experiment_id": exp.experiment_id,
                    "name": exp.name,
                    "lifecycle_stage": exp.lifecycle_stage
                } for exp in experiments
            ]
        }
    except Exception as e:
        logger.error(f"Experiments listing error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "service": "V5_Trader API Gateway",
        "version": "1.0.0",
        "status": "operational",
        "timestamp": datetime.now(),
        "endpoints": {
            "health": "/health",
            "metrics": "/metrics",
            "docs": "/docs",
            "predict": "/api/predict",
            "market_data": "/api/market-data",
            "models": "/api/models",
            "experiments": "/api/experiments"
        }
    }

# Global startup time for uptime calculation
startup_time = time.time()

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
