{"dashboard": {"id": null, "title": "V5_Trader System Overview", "tags": ["v5trader", "trading", "ai", "monitoring"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "API Gateway - Request Rate", "type": "stat", "targets": [{"expr": "rate(http_requests_total{job=\"v5trader-api-gateway\"}[5m])", "legendFormat": "Requests/sec"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "API Gateway - Response Time", "type": "stat", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"v5trader-api-gateway\"}[5m]))", "legendFormat": "95th percentile"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.1}, {"color": "red", "value": 0.5}]}, "unit": "s"}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Active Connections", "type": "stat", "targets": [{"expr": "active_connections{job=\"v5trader-api-gateway\"}", "legendFormat": "Active"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 500}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Model Predictions", "type": "stat", "targets": [{"expr": "rate(model_predictions_total{job=\"v5trader-api-gateway\"}[5m])", "legendFormat": "Predictions/sec"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 20}]}, "unit": "ops"}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "HTTP Status Codes", "type": "timeseries", "targets": [{"expr": "rate(http_requests_total{job=\"v5trader-api-gateway\"}[5m])", "legendFormat": "{{status}} - {{method}} {{endpoint}}"}], "fieldConfig": {"defaults": {"custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "color": {"mode": "palette-classic"}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 6, "title": "Database Connections", "type": "timeseries", "targets": [{"expr": "pg_stat_database_numbackends{job=\"v5trader-postgres\"}", "legendFormat": "{{datname}}"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 7, "title": "Redis Memory Usage", "type": "timeseries", "targets": [{"expr": "redis_memory_used_bytes{job=\"v5trader-redis\"}", "legendFormat": "Used Memory"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "bytes"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 8, "title": "System CPU Usage", "type": "timeseries", "targets": [{"expr": "100 - (avg by (instance) (rate(node_cpu_seconds_total{mode=\"idle\",job=\"v5trader-system\"}[5m])) * 100)", "legendFormat": "CPU Usage %"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}]}}