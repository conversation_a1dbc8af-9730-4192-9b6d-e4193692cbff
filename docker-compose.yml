version: '3.8'

networks:
  v5trader_network:
    driver: bridge

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: v5trader_postgres
    environment:
      POSTGRES_DB: v5trader
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-v5trader_secure_2024}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - v5trader_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: v5trader_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-v5trader_redis_2024}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - v5trader_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MLflow Tracking Server
  mlflow:
    image: python:3.11-slim
    container_name: v5trader_mlflow
    working_dir: /mlflow
    command: >
      bash -c "
        pip install mlflow psycopg2-binary prometheus_client &&
        mlflow server
          --backend-store-uri postgresql://postgres:${POSTGRES_PASSWORD:-v5trader_secure_2024}@postgres:5432/v5trader
          --default-artifact-root /mlflow/artifacts
          --host 0.0.0.0
          --port 5000
          --serve-artifacts
      "
    volumes:
      - mlflow_artifacts:/mlflow/artifacts
    ports:
      - "5000:5000"
    networks:
      - v5trader_network
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      - MLFLOW_TRACKING_URI=http://localhost:5000
    restart: unless-stopped

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: v5trader_prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - v5trader_network
    restart: unless-stopped

  # Grafana Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: v5trader_grafana
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-v5trader_grafana_2024}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3000:3000"
    networks:
      - v5trader_network
    depends_on:
      - prometheus
    restart: unless-stopped

  # API Gateway with Metrics
  api-gateway:
    build:
      context: .
      dockerfile: services/api-gateway/Dockerfile
    container_name: v5trader_api_gateway
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-v5trader_secure_2024}@postgres:5432/v5trader
      - REDIS_URL=redis://:${REDIS_PASSWORD:-v5trader_redis_2024}@redis:6379/0
      - MLFLOW_TRACKING_URI=http://mlflow:5000
      - PROMETHEUS_MULTIPROC_DIR=/tmp
    networks:
      - v5trader_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      mlflow:
        condition: service_started
    volumes:
      - ./services/api-gateway:/app
      - ./models:/app/models
      - ./data:/app/data
    restart: unless-stopped

  # Node Exporter for System Metrics
  node_exporter:
    image: prom/node-exporter:latest
    container_name: v5trader_node_exporter
    command:
      - '--path.rootfs=/host'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    volumes:
      - '/:/host:ro,rslave'
    ports:
      - "9100:9100"
    networks:
      - v5trader_network
    restart: unless-stopped

  # PostgreSQL Exporter
  postgres_exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: v5trader_postgres_exporter
    environment:
      - DATA_SOURCE_NAME=postgresql://postgres:${POSTGRES_PASSWORD:-v5trader_secure_2024}@postgres:5432/v5trader?sslmode=disable
    ports:
      - "9187:9187"
    networks:
      - v5trader_network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped

  # Redis Exporter
  redis_exporter:
    image: oliver006/redis_exporter:latest
    container_name: v5trader_redis_exporter
    environment:
      - REDIS_ADDR=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-v5trader_redis_2024}
    ports:
      - "9121:9121"
    networks:
      - v5trader_network
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  mlflow_artifacts:
  prometheus_data:
  grafana_data:
