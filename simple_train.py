#!/usr/bin/env python3
"""
🧠 V5_Trader - Einfaches Model Training
Trainiert ein einfaches Modell ohne MLflow-Abhängigkeit
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, classification_report
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def load_and_prepare_data():
    """Lädt und bereitet die Marktdaten vor"""
    print("📊 Lade Marktdaten...")
    
    data_path = Path("data/market_data.csv")
    if not data_path.exists():
        print("❌ Marktdaten nicht gefunden!")
        return None
    
    df = pd.read_csv(data_path)
    df['Date'] = pd.to_datetime(df['Date'])
    df = df.sort_values('Date').reset_index(drop=True)
    
    print(f"✅ {len(df)} Datenpunkte geladen")
    print(f"📅 Zeitraum: {df['Date'].min().date()} bis {df['Date'].max().date()}")
    
    # Technische Indikatoren
    print("🔧 Berechne Features...")
    
    # Returns
    df['Returns'] = df['Close'].pct_change()
    df['Returns_1'] = df['Returns'].shift(1)
    df['Returns_2'] = df['Returns'].shift(2)
    df['Returns_3'] = df['Returns'].shift(3)
    
    # Moving Averages
    df['SMA_5'] = df['Close'].rolling(5).mean()
    df['SMA_10'] = df['Close'].rolling(10).mean()
    df['SMA_20'] = df['Close'].rolling(20).mean()
    
    # Price Ratios
    df['Price_SMA5_Ratio'] = df['Close'] / df['SMA_5']
    df['Price_SMA10_Ratio'] = df['Close'] / df['SMA_10']
    df['Price_SMA20_Ratio'] = df['Close'] / df['SMA_20']
    
    # Volatility
    df['Volatility_5'] = df['Returns'].rolling(5).std()
    df['Volatility_10'] = df['Returns'].rolling(10).std()
    df['Volatility_20'] = df['Returns'].rolling(20).std()
    
    # Volume Features
    df['Volume_SMA'] = df['Volume'].rolling(10).mean()
    df['Volume_Ratio'] = df['Volume'] / df['Volume_SMA']
    
    # RSI
    delta = df['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['RSI'] = 100 - (100 / (1 + rs))
    
    # Target: Nächster Tag steigt (1) oder fällt (0)
    df['Target'] = (df['Close'].shift(-1) > df['Close']).astype(int)
    
    # Entferne NaN-Werte
    df = df.dropna()
    
    print(f"✅ {len(df)} bereinigte Datenpunkte")
    return df

class SimpleNeuralNetwork(nn.Module):
    """Einfaches Neural Network für Trading Predictions"""
    
    def __init__(self, input_size, hidden_size=64, dropout=0.2):
        super().__init__()
        self.network = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, hidden_size // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 4, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        return self.network(x).squeeze()

def train_model():
    """Haupttraining-Funktion"""
    print("🚀 Starte einfaches Model Training...\n")
    
    # Daten laden
    df = load_and_prepare_data()
    if df is None:
        return
    
    # Features auswählen
    feature_cols = [
        'Returns', 'Returns_1', 'Returns_2', 'Returns_3',
        'Price_SMA5_Ratio', 'Price_SMA10_Ratio', 'Price_SMA20_Ratio',
        'Volatility_5', 'Volatility_10', 'Volatility_20',
        'Volume_Ratio', 'RSI'
    ]
    
    print(f"📊 Verwende {len(feature_cols)} Features:")
    for i, col in enumerate(feature_cols, 1):
        print(f"   {i:2d}. {col}")
    
    # Daten vorbereiten
    X = df[feature_cols].values
    y = df['Target'].values
    
    # Train/Test Split
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"\n📊 Daten-Split:")
    print(f"   Training: {len(X_train)} samples")
    print(f"   Test: {len(X_test)} samples")
    print(f"   Target-Verteilung Train: {np.bincount(y_train)}")
    print(f"   Target-Verteilung Test: {np.bincount(y_test)}")
    
    # Normalisierung
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # PyTorch Tensors
    X_train_tensor = torch.FloatTensor(X_train_scaled)
    X_test_tensor = torch.FloatTensor(X_test_scaled)
    y_train_tensor = torch.FloatTensor(y_train)
    y_test_tensor = torch.FloatTensor(y_test)
    
    # Model
    model = SimpleNeuralNetwork(input_size=len(feature_cols))
    criterion = nn.BCELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    print(f"\n🧠 Model-Architektur:")
    print(f"   Input Size: {len(feature_cols)}")
    print(f"   Hidden Layers: 64 -> 32 -> 16 -> 1")
    print(f"   Activation: ReLU + Sigmoid")
    print(f"   Optimizer: Adam (lr=0.001)")
    
    # Training
    print(f"\n🏋️ Starte Training...")
    
    epochs = 100
    batch_size = 32
    train_losses = []
    train_accuracies = []
    
    for epoch in range(epochs):
        model.train()
        epoch_loss = 0
        epoch_correct = 0
        
        # Mini-batch training
        for i in range(0, len(X_train_tensor), batch_size):
            batch_X = X_train_tensor[i:i+batch_size]
            batch_y = y_train_tensor[i:i+batch_size]
            
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
            predictions = (outputs > 0.5).float()
            epoch_correct += (predictions == batch_y).sum().item()
        
        # Epoch-Metriken
        avg_loss = epoch_loss / (len(X_train_tensor) // batch_size)
        accuracy = epoch_correct / len(X_train_tensor)
        
        train_losses.append(avg_loss)
        train_accuracies.append(accuracy)
        
        if (epoch + 1) % 20 == 0:
            print(f"   Epoch {epoch+1:3d}/{epochs}: Loss = {avg_loss:.4f}, Accuracy = {accuracy:.4f}")
    
    # Test-Evaluation
    print(f"\n📊 Evaluiere auf Test-Daten...")
    model.eval()
    with torch.no_grad():
        test_outputs = model(X_test_tensor)
        test_predictions = (test_outputs > 0.5).float().numpy()
        test_accuracy = accuracy_score(y_test, test_predictions)
        
        print(f"   Test Accuracy: {test_accuracy:.4f}")
        print(f"\n📋 Classification Report:")
        print(classification_report(y_test, test_predictions, 
                                  target_names=['Down', 'Up']))
    
    # Feature Importance (vereinfacht)
    print(f"\n🎯 Feature Importance (Gewichte der ersten Schicht):")
    first_layer_weights = model.network[0].weight.data.abs().mean(dim=0)
    feature_importance = list(zip(feature_cols, first_layer_weights.numpy()))
    feature_importance.sort(key=lambda x: x[1], reverse=True)
    
    for i, (feature, importance) in enumerate(feature_importance[:8], 1):
        print(f"   {i:2d}. {feature:<20}: {importance:.4f}")
    
    # Visualisierungen
    print(f"\n📈 Erstelle Visualisierungen...")
    
    plt.style.use('seaborn-v0_8')
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('V5_Trader - Model Training Ergebnisse', fontsize=16, fontweight='bold')
    
    # Training Loss
    axes[0, 0].plot(train_losses, label='Training Loss', color='red', alpha=0.7)
    axes[0, 0].set_title('Training Loss')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].legend()
    
    # Training Accuracy
    axes[0, 1].plot(train_accuracies, label='Training Accuracy', color='blue', alpha=0.7)
    axes[0, 1].axhline(y=0.5, color='gray', linestyle='--', alpha=0.5, label='Random Baseline')
    axes[0, 1].set_title('Training Accuracy')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('Accuracy')
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].legend()
    
    # Feature Importance
    features, importances = zip(*feature_importance[:8])
    axes[1, 0].barh(range(len(features)), importances, color='green', alpha=0.7)
    axes[1, 0].set_yticks(range(len(features)))
    axes[1, 0].set_yticklabels(features)
    axes[1, 0].set_title('Top 8 Feature Importance')
    axes[1, 0].set_xlabel('Importance')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Prediction Distribution
    axes[1, 1].hist(test_outputs.numpy(), bins=30, alpha=0.7, color='purple', edgecolor='black')
    axes[1, 1].axvline(x=0.5, color='red', linestyle='--', label='Decision Threshold')
    axes[1, 1].set_title('Prediction Distribution (Test Set)')
    axes[1, 1].set_xlabel('Predicted Probability')
    axes[1, 1].set_ylabel('Frequency')
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].legend()
    
    plt.tight_layout()
    plt.savefig('model_training_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Model speichern
    model_path = Path("models")
    model_path.mkdir(exist_ok=True)
    
    torch.save({
        'model_state_dict': model.state_dict(),
        'scaler': scaler,
        'feature_cols': feature_cols,
        'test_accuracy': test_accuracy,
        'feature_importance': feature_importance
    }, model_path / "simple_trading_model.pth")
    
    print(f"\n✅ Training abgeschlossen!")
    print(f"💾 Modell gespeichert: {model_path / 'simple_trading_model.pth'}")
    print(f"📊 Visualisierungen gespeichert: model_training_results.png")
    print(f"🎯 Test Accuracy: {test_accuracy:.4f}")
    
    return model, scaler, feature_cols, test_accuracy

if __name__ == "__main__":
    train_model()
