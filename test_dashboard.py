#!/usr/bin/env python3
"""
Test script for the V5_Trader web dashboard
Tests WebSocket connectivity and dashboard functionality
"""
import asyncio
import websockets
import json
import time
import webbrowser
from pathlib import Path

async def test_websocket_connection():
    """Test WebSocket connection and data flow"""
    print("🧪 Testing WebSocket connection...")
    
    try:
        # Connect to WebSocket server
        uri = "ws://localhost:8765"
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket connection established")
            
            # Send subscription message
            subscribe_msg = {
                "type": "subscribe",
                "symbols": ["AAPL", "MSFT", "GOOGL"]
            }
            await websocket.send(json.dumps(subscribe_msg))
            print("📡 Subscription message sent")
            
            # Listen for messages for 10 seconds
            print("👂 Listening for messages...")
            start_time = time.time()
            message_count = 0
            
            while time.time() - start_time < 10:
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    data = json.loads(message)
                    message_count += 1
                    
                    if data.get("type") == "market_update":
                        symbol = data.get("symbol", "UNKNOWN")
                        price = data.get("price", 0)
                        change = data.get("change", 0)
                        print(f"📈 {symbol}: ${price:.2f} ({change:+.2f}%)")
                    elif data.get("type") == "welcome":
                        print("👋 Welcome message received")
                    elif data.get("type") == "subscription_confirmed":
                        print(f"✅ Subscription confirmed: {data.get('symbols', [])}")
                    
                except asyncio.TimeoutError:
                    continue
            
            print(f"📊 Received {message_count} messages in 10 seconds")
            
            # Test status request
            status_msg = {"type": "get_status"}
            await websocket.send(json.dumps(status_msg))
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                status_data = json.loads(response)
                if status_data.get("type") == "status":
                    print(f"📊 Server status: {status_data.get('connected_clients', 0)} clients")
            except asyncio.TimeoutError:
                print("⚠️ No status response received")
            
            return True
            
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
        return False

def test_dashboard_files():
    """Test that dashboard files exist and are accessible"""
    print("\n📁 Testing dashboard files...")
    
    dashboard_path = Path("static/dashboard.html")
    
    if not dashboard_path.exists():
        print("❌ Dashboard file not found")
        return False
    
    print("✅ Dashboard file exists")
    
    # Check file size
    file_size = dashboard_path.stat().st_size
    print(f"📏 Dashboard file size: {file_size} bytes")
    
    if file_size < 1000:
        print("⚠️ Dashboard file seems too small")
        return False
    
    # Check for key HTML elements
    try:
        with open(dashboard_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        required_elements = [
            "V5 Trader Dashboard",
            "WebSocket",
            "System Status",
            "Portfolio",
            "Live Kurse",
            "Trading Signale"
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ Missing dashboard elements: {missing_elements}")
            return False
        
        print("✅ All required dashboard elements found")
        return True
        
    except Exception as e:
        print(f"❌ Error reading dashboard file: {e}")
        return False

def open_dashboard():
    """Open dashboard in browser"""
    print("\n🌐 Opening dashboard in browser...")
    
    try:
        dashboard_path = Path("static/dashboard.html").absolute()
        dashboard_url = f"file://{dashboard_path}"
        
        webbrowser.open(dashboard_url)
        print(f"✅ Dashboard opened: {dashboard_url}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to open dashboard: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 V5_TRADER DASHBOARD TEST")
    print("=" * 50)
    
    # Test dashboard files
    dashboard_files_ok = test_dashboard_files()
    
    # Test WebSocket connection
    websocket_ok = await test_websocket_connection()
    
    # Open dashboard
    dashboard_opened = open_dashboard()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DASHBOARD TEST SUMMARY")
    print("=" * 50)
    
    tests = [
        ("Dashboard Files", dashboard_files_ok),
        ("WebSocket Connection", websocket_ok),
        ("Dashboard Browser", dashboard_opened)
    ]
    
    passed = 0
    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<20}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("\n🎉 DASHBOARD TEST SUCCESSFUL!")
        print("🌐 Dashboard is ready for use!")
        print("\n📋 Next steps:")
        print("  1. Keep WebSocket server running")
        print("  2. Open dashboard in browser")
        print("  3. Watch live market data updates")
        print("  4. Monitor system status")
    else:
        print(f"\n❌ DASHBOARD TEST FAILED!")
        print("Please fix the failing tests before using the dashboard.")
    
    return passed == len(tests)

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        exit(1)
