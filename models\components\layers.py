#!/usr/bin/env python3
"""
Common neural network layers for V5_Trader models
Reusable components for model architecture
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional

class PositionalEncoding(nn.Module):
    """Positional encoding for transformer models"""
    
    def __init__(self, d_model: int, max_len: int = 5000, dropout: float = 0.1):
        super().__init__()
        self.dropout = nn.Dropout(dropout)
        
        # Create positional encoding matrix
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)

class FeedForward(nn.Module):
    """Position-wise feed-forward network"""
    
    def __init__(self, d_model: int, d_ff: int, dropout: float = 0.1, activation: str = 'relu'):
        super().__init__()
        self.linear1 = nn.Linear(d_model, d_ff)
        self.linear2 = nn.Linear(d_ff, d_model)
        self.dropout = nn.Dropout(dropout)
        
        if activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'gelu':
            self.activation = nn.GELU()
        elif activation == 'swish':
            self.activation = nn.SiLU()
        else:
            raise ValueError(f"Unsupported activation: {activation}")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.linear2(self.dropout(self.activation(self.linear1(x))))

class ResidualConnection(nn.Module):
    """Residual connection with layer normalization"""
    
    def __init__(self, d_model: int, dropout: float = 0.1):
        super().__init__()
        self.norm = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x: torch.Tensor, sublayer_output: torch.Tensor) -> torch.Tensor:
        return self.norm(x + self.dropout(sublayer_output))

class ConvolutionalBlock(nn.Module):
    """1D Convolutional block for feature extraction"""
    
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3,
                 stride: int = 1, padding: int = 1, dropout: float = 0.1):
        super().__init__()
        self.conv = nn.Conv1d(in_channels, out_channels, kernel_size, stride, padding)
        self.batch_norm = nn.BatchNorm1d(out_channels)
        self.activation = nn.ReLU()
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x shape: (batch_size, seq_len, features)
        # Conv1d expects: (batch_size, features, seq_len)
        x = x.transpose(1, 2)
        x = self.conv(x)
        x = self.batch_norm(x)
        x = self.activation(x)
        x = self.dropout(x)
        # Convert back to (batch_size, seq_len, features)
        x = x.transpose(1, 2)
        return x

class LSTMBlock(nn.Module):
    """LSTM block with optional bidirectionality"""
    
    def __init__(self, input_size: int, hidden_size: int, num_layers: int = 1,
                 dropout: float = 0.1, bidirectional: bool = False):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.bidirectional = bidirectional
        
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=bidirectional,
            batch_first=True
        )
        
        # Output projection if bidirectional
        if bidirectional:
            self.output_projection = nn.Linear(hidden_size * 2, hidden_size)
        else:
            self.output_projection = None
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x shape: (batch_size, seq_len, input_size)
        lstm_out, _ = self.lstm(x)
        
        if self.output_projection is not None:
            lstm_out = self.output_projection(lstm_out)
        
        return lstm_out

class GatedLinearUnit(nn.Module):
    """Gated Linear Unit for controlling information flow"""
    
    def __init__(self, input_size: int, output_size: int):
        super().__init__()
        self.linear = nn.Linear(input_size, output_size * 2)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.linear(x)
        # Split into two parts
        value, gate = x.chunk(2, dim=-1)
        # Apply sigmoid gate
        return value * torch.sigmoid(gate)

class TimeDistributed(nn.Module):
    """Apply a layer to each time step independently"""
    
    def __init__(self, layer: nn.Module):
        super().__init__()
        self.layer = layer
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x shape: (batch_size, seq_len, input_size)
        batch_size, seq_len = x.size(0), x.size(1)
        
        # Reshape to (batch_size * seq_len, input_size)
        x_reshaped = x.view(-1, x.size(-1))
        
        # Apply layer
        output = self.layer(x_reshaped)
        
        # Reshape back to (batch_size, seq_len, output_size)
        output = output.view(batch_size, seq_len, -1)
        
        return output

class MultiScaleConv1D(nn.Module):
    """Multi-scale 1D convolution for capturing different temporal patterns"""
    
    def __init__(self, in_channels: int, out_channels: int, kernel_sizes: list = [3, 5, 7]):
        super().__init__()
        self.convs = nn.ModuleList([
            nn.Conv1d(in_channels, out_channels // len(kernel_sizes), 
                     kernel_size=k, padding=k//2)
            for k in kernel_sizes
        ])
        self.batch_norm = nn.BatchNorm1d(out_channels)
        self.activation = nn.ReLU()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x shape: (batch_size, seq_len, features)
        x = x.transpose(1, 2)  # (batch_size, features, seq_len)
        
        # Apply convolutions with different kernel sizes
        conv_outputs = [conv(x) for conv in self.convs]
        
        # Concatenate outputs
        x = torch.cat(conv_outputs, dim=1)
        
        # Batch norm and activation
        x = self.batch_norm(x)
        x = self.activation(x)
        
        # Convert back to (batch_size, seq_len, features)
        x = x.transpose(1, 2)
        
        return x

class AdaptivePooling(nn.Module):
    """Adaptive pooling layer for sequence length normalization"""
    
    def __init__(self, output_size: int, pooling_type: str = 'avg'):
        super().__init__()
        self.output_size = output_size
        
        if pooling_type == 'avg':
            self.pool = nn.AdaptiveAvgPool1d(output_size)
        elif pooling_type == 'max':
            self.pool = nn.AdaptiveMaxPool1d(output_size)
        else:
            raise ValueError(f"Unsupported pooling type: {pooling_type}")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x shape: (batch_size, seq_len, features)
        x = x.transpose(1, 2)  # (batch_size, features, seq_len)
        x = self.pool(x)
        x = x.transpose(1, 2)  # (batch_size, output_size, features)
        return x
