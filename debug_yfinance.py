import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta

# Test what yfinance returns
end = datetime.today().date()
start = end - timedelta(days=365)

print("Testing yfinance data structure...")
data = yf.download("AAPL", start=start.isoformat(), end=end.isoformat())
print("Raw data columns:", data.columns.tolist())
print("Raw data index:", data.index.name)
print("Raw data dtypes:\n", data.dtypes)
print("Raw data shape:", data.shape)
print("First few rows:\n", data.head())

# Reset index
data_reset = data.reset_index()
print("\nAfter reset_index:")
print("Columns:", data_reset.columns.tolist())
print("Dtypes:\n", data_reset.dtypes)
print("First few rows:\n", data_reset.head())
