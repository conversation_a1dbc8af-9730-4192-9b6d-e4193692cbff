#!/usr/bin/env python3
"""
Complete system validation test for V5_Trader
Tests all major components and their integration
"""
import os
import sys
import time
import asyncio
import subprocess
from datetime import datetime

def test_file_structure():
    """Test that all required files and directories exist"""
    print("📁 Testing file structure...")
    
    required_files = [
        "train_and_register.py",
        "fetch_market_data.py",
        "data/market_data.csv",
        "strategies/base_strategy.py",
        "strategies/technical_indicators.py",
        "services/websocket_server.py",
        "tools/system_monitor.py",
        "tools/dashboard.py",
        "tools/websocket_client.py",
        "tests/integration/test_system.py",
        "test_trading_strategy.py",
        "run_tests.py"
    ]
    
    required_dirs = [
        "data/",
        "models/",
        "strategies/",
        "services/",
        "tools/",
        "tests/",
        "tests/integration/",
        "logs/"
    ]
    
    missing_files = []
    missing_dirs = []
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    if missing_dirs:
        print(f"❌ Missing directories: {missing_dirs}")
        return False
    
    print("✅ All required files and directories exist")
    return True


def test_imports():
    """Test that all modules can be imported"""
    print("📦 Testing imports...")
    
    modules_to_test = [
        "strategies.base_strategy",
        "strategies.technical_indicators",
        "data.fin_data_module",
        "models.ensemble.advanced_transformer",
        "tools.system_monitor"
    ]
    
    failed_imports = []
    
    for module in modules_to_test:
        try:
            __import__(module)
        except Exception as e:
            failed_imports.append((module, str(e)))
    
    if failed_imports:
        print("❌ Failed imports:")
        for module, error in failed_imports:
            print(f"   {module}: {error}")
        return False
    
    print("✅ All modules import successfully")
    return True


def test_data_pipeline():
    """Test the data pipeline"""
    print("🔄 Testing data pipeline...")
    
    try:
        # Test data loading
        import pandas as pd
        df = pd.read_csv("data/market_data.csv")
        
        if len(df) == 0:
            print("❌ Market data is empty")
            return False
        
        required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'Ticker', 'Target']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"❌ Missing columns: {missing_columns}")
            return False
        
        print(f"✅ Data pipeline working - {len(df)} rows loaded")
        return True
        
    except Exception as e:
        print(f"❌ Data pipeline failed: {e}")
        return False


def test_model_system():
    """Test the model system"""
    print("🤖 Testing model system...")
    
    try:
        from models.ensemble.advanced_transformer import AdvancedFinancialTransformer
        
        # Test model creation
        params = {
            "input_features": 5,
            "sequence_length": 60,
            "d_model": 64,
            "n_heads": 4,
            "n_encoder_layers": 2,
            "dropout": 0.1,
            "cnn_filters": 32,
            "lstm_units": 64,
            "output_size": 1,
            "learning_rate": 1e-4,
            "weight_decay": 1e-5,
            "use_mixed_precision": False,
            "uncertainty_estimation": True,
            "ensemble_size": 3
        }
        
        model = AdvancedFinancialTransformer(**params)
        print("✅ Model system working")
        return True
        
    except Exception as e:
        print(f"❌ Model system failed: {e}")
        return False


def test_trading_strategies():
    """Test trading strategies"""
    print("📈 Testing trading strategies...")
    
    try:
        from strategies.technical_indicators import SimpleMovingAverageStrategy
        
        strategy_params = {"short_window": 5, "long_window": 10}
        strategy = SimpleMovingAverageStrategy(strategy_params)
        
        # Test with sample data
        import pandas as pd
        sample_data = pd.DataFrame({
            'Close': [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110]
        })
        
        signal = strategy.generate_signals(sample_data)
        
        if "signal" not in signal or "confidence" not in signal:
            print("❌ Strategy signal format invalid")
            return False
        
        print("✅ Trading strategies working")
        return True
        
    except Exception as e:
        print(f"❌ Trading strategies failed: {e}")
        return False


def run_complete_system_test():
    """Run complete system validation"""
    print("🚀 V5_TRADER COMPLETE SYSTEM VALIDATION")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Module Imports", test_imports),
        ("Data Pipeline", test_data_pipeline),
        ("Model System", test_model_system),
        ("Trading Strategies", test_trading_strategies),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 SYSTEM VALIDATION SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name:<20}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 SYSTEM VALIDATION SUCCESSFUL!")
        print("🚀 V5_Trader is ready for production!")
        
        print("\n📋 Available Commands:")
        print("  python train_and_register.py          - Train models")
        print("  python test_trading_strategy.py       - Test strategies")
        print("  python services/websocket_server.py   - Start WebSocket server")
        print("  python tools/dashboard.py             - System dashboard")
        print("  python tools/system_monitor.py        - System monitoring")
        print("  start_system.bat                      - Start complete system")
        
        return True
    else:
        print(f"\n❌ SYSTEM VALIDATION FAILED!")
        print(f"Please fix the {total - passed} failing test(s) before proceeding.")
        return False


if __name__ == "__main__":
    success = run_complete_system_test()
    sys.exit(0 if success else 1)
