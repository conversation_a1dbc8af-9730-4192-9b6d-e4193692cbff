#!/usr/bin/env python3
"""
WebSocket client for testing the V5_Trader real-time data feed
"""
import asyncio
import websockets
import json
from datetime import datetime


class MarketDataClient:
    """WebSocket client for receiving market data"""
    
    def __init__(self, uri="ws://localhost:8765"):
        self.uri = uri
        self.websocket = None
        self.running = False
        
    async def connect(self):
        """Connect to WebSocket server"""
        try:
            self.websocket = await websockets.connect(self.uri)
            self.running = True
            print(f"✅ Connected to {self.uri}")
            return True
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from WebSocket server"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            print("🔌 Disconnected")
    
    async def send_message(self, message):
        """Send message to server"""
        if self.websocket:
            await self.websocket.send(json.dumps(message))
    
    async def listen_for_messages(self):
        """Listen for incoming messages"""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                await self.handle_message(data)
        except websockets.exceptions.ConnectionClosed:
            print("🔌 Connection closed by server")
            self.running = False
        except Exception as e:
            print(f"❌ Error receiving message: {e}")
    
    async def handle_message(self, data):
        """Handle incoming message"""
        message_type = data.get("type", "unknown")
        
        if message_type == "welcome":
            print("📡 Welcome message received")
            print(f"   Current symbols: {list(data.get('current_data', {}).keys())}")
            
        elif message_type == "market_update":
            symbol = data.get("symbol", "UNKNOWN")
            price = data.get("price", 0)
            change = data.get("change", 0)
            timestamp = data.get("timestamp", "")
            
            # Format timestamp
            try:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                time_str = dt.strftime("%H:%M:%S")
            except:
                time_str = timestamp[:8] if len(timestamp) > 8 else timestamp
            
            # Color coding for price changes
            color = "🟢" if change >= 0 else "🔴"
            print(f"{color} {symbol}: ${price:8.2f} ({change:+.2f}%) [{time_str}]")
            
        elif message_type == "status":
            print("📊 Server Status:")
            print(f"   Connected clients: {data.get('connected_clients', 0)}")
            print(f"   Active symbols: {data.get('active_symbols', [])}")
            print(f"   Server time: {data.get('server_time', 'unknown')}")
            
        elif message_type == "subscription_confirmed":
            symbols = data.get("symbols", [])
            print(f"✅ Subscription confirmed for: {', '.join(symbols)}")
            
        else:
            print(f"📨 Unknown message type: {message_type}")
            print(f"   Data: {data}")
    
    async def subscribe_to_symbols(self, symbols):
        """Subscribe to specific symbols"""
        message = {
            "type": "subscribe",
            "symbols": symbols
        }
        await self.send_message(message)
        print(f"📡 Subscribing to: {', '.join(symbols)}")
    
    async def get_server_status(self):
        """Request server status"""
        message = {"type": "get_status"}
        await self.send_message(message)
    
    async def run_interactive_session(self):
        """Run interactive session with user commands"""
        print("\n" + "="*50)
        print("📡 V5_TRADER WEBSOCKET CLIENT")
        print("="*50)
        print("Commands:")
        print("  status  - Get server status")
        print("  sub     - Subscribe to symbols")
        print("  quit    - Exit client")
        print("="*50)
        
        while self.running:
            try:
                # Non-blocking input simulation
                await asyncio.sleep(0.1)
                
                # In a real implementation, you'd use aioconsole for async input
                # For now, we'll just listen to messages
                
            except KeyboardInterrupt:
                print("\n🛑 Exiting...")
                break
    
    async def run_demo(self):
        """Run a demo session"""
        if not await self.connect():
            return
        
        try:
            # Start listening for messages
            listen_task = asyncio.create_task(self.listen_for_messages())
            
            # Wait a bit for welcome message
            await asyncio.sleep(1)
            
            # Subscribe to some symbols
            await self.subscribe_to_symbols(["AAPL", "MSFT", "GOOGL"])
            
            # Get server status
            await asyncio.sleep(1)
            await self.get_server_status()
            
            # Run for 30 seconds
            print(f"\n📊 Receiving market data for 30 seconds...")
            print("Press Ctrl+C to stop early")
            
            try:
                await asyncio.wait_for(listen_task, timeout=30.0)
            except asyncio.TimeoutError:
                print("\n⏰ Demo session completed")
            
        except KeyboardInterrupt:
            print("\n🛑 Demo stopped by user")
        finally:
            await self.disconnect()


async def main():
    """Main function"""
    client = MarketDataClient()
    
    print("🚀 Starting WebSocket Client Demo")
    print("Make sure the WebSocket server is running:")
    print("  python services/websocket_server.py")
    print()
    
    await client.run_demo()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
