#!/usr/bin/env python3
"""
🤖 V5_Trader - ML-basierte Trading Strategie
Verwendet das trainierte Modell für Trading-Entscheidungen
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class SimpleNeuralNetwork(nn.Module):
    """Einfaches Neural Network für Trading Predictions"""
    
    def __init__(self, input_size, hidden_size=64, dropout=0.2):
        super().__init__()
        self.network = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, hidden_size // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 4, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        return self.network(x).squeeze()

class MLTradingStrategy:
    """ML-basierte Trading-Strategie"""
    
    def __init__(self, model_path="models/simple_trading_model.pth", 
                 confidence_threshold=0.6, min_hold_days=3):
        self.model_path = Path(model_path)
        self.confidence_threshold = confidence_threshold
        self.min_hold_days = min_hold_days
        self.model = None
        self.scaler = None
        self.feature_cols = None
        self.last_signal_date = None
        self.current_position = 0  # 0=neutral, 1=long, -1=short
        
        self._load_model()
    
    def _load_model(self):
        """Lädt das trainierte Modell"""
        if not self.model_path.exists():
            raise FileNotFoundError(f"Model nicht gefunden: {self.model_path}")
        
        checkpoint = torch.load(self.model_path, map_location='cpu', weights_only=False)
        
        self.scaler = checkpoint['scaler']
        self.feature_cols = checkpoint['feature_cols']
        
        # Model initialisieren
        self.model = SimpleNeuralNetwork(input_size=len(self.feature_cols))
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()
        
        print(f"✅ Modell geladen: {self.model_path}")
        print(f"📊 Features: {len(self.feature_cols)}")
        print(f"🎯 Test Accuracy: {checkpoint.get('test_accuracy', 'N/A'):.4f}")
    
    def calculate_features(self, df):
        """Berechnet die Features für das Modell"""
        # Returns
        df['Returns'] = df['Close'].pct_change()
        df['Returns_1'] = df['Returns'].shift(1)
        df['Returns_2'] = df['Returns'].shift(2)
        df['Returns_3'] = df['Returns'].shift(3)
        
        # Moving Averages
        df['SMA_5'] = df['Close'].rolling(5).mean()
        df['SMA_10'] = df['Close'].rolling(10).mean()
        df['SMA_20'] = df['Close'].rolling(20).mean()
        
        # Price Ratios
        df['Price_SMA5_Ratio'] = df['Close'] / df['SMA_5']
        df['Price_SMA10_Ratio'] = df['Close'] / df['SMA_10']
        df['Price_SMA20_Ratio'] = df['Close'] / df['SMA_20']
        
        # Volatility
        df['Volatility_5'] = df['Returns'].rolling(5).std()
        df['Volatility_10'] = df['Returns'].rolling(10).std()
        df['Volatility_20'] = df['Returns'].rolling(20).std()
        
        # Volume Features
        df['Volume_SMA'] = df['Volume'].rolling(10).mean()
        df['Volume_Ratio'] = df['Volume'] / df['Volume_SMA']
        
        # RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        
        return df
    
    def generate_signal(self, df, current_date):
        """Generiert Trading-Signal für aktuelles Datum"""
        # Features berechnen
        df = self.calculate_features(df)
        
        # Aktueller Datenpunkt
        current_idx = df[df['Date'] <= current_date].index[-1]
        current_row = df.loc[current_idx]
        
        # Prüfe ob genug Daten vorhanden
        if pd.isna(current_row[self.feature_cols]).any():
            return 0, 0.5  # Neutral signal, 50% confidence
        
        # Features für Modell vorbereiten
        features = current_row[self.feature_cols].values.reshape(1, -1)
        features_scaled = self.scaler.transform(features)
        features_tensor = torch.FloatTensor(features_scaled)
        
        # Vorhersage
        with torch.no_grad():
            prediction = self.model(features_tensor).item()
        
        # Signal basierend auf Confidence Threshold
        if prediction > self.confidence_threshold:
            signal = 1  # Buy
        elif prediction < (1 - self.confidence_threshold):
            signal = -1  # Sell
        else:
            signal = 0  # Hold
        
        # Minimum Hold Period berücksichtigen
        if self.last_signal_date is not None:
            days_since_last = (current_date - self.last_signal_date).days
            if days_since_last < self.min_hold_days and signal != self.current_position:
                signal = self.current_position  # Behalte aktuelle Position
        
        # Position und Datum aktualisieren
        if signal != self.current_position:
            self.current_position = signal
            self.last_signal_date = current_date
        
        return signal, prediction

def run_ml_backtest():
    """Führt Backtest mit ML-Strategie durch"""
    print("🤖 V5_Trader ML-Strategie Backtest\n")
    
    # Daten laden
    print("📊 Lade Marktdaten...")
    data_path = Path("data/market_data.csv")
    df = pd.read_csv(data_path)
    df['Date'] = pd.to_datetime(df['Date'])
    df = df.sort_values('Date').reset_index(drop=True)
    
    print(f"✅ {len(df)} Datenpunkte geladen")
    print(f"📅 Zeitraum: {df['Date'].min().date()} bis {df['Date'].max().date()}")
    
    # ML-Strategie initialisieren
    try:
        strategy = MLTradingStrategy(
            confidence_threshold=0.65,  # Höhere Confidence für weniger Trades
            min_hold_days=5  # Minimum 5 Tage halten
        )
    except FileNotFoundError:
        print("❌ Modell nicht gefunden! Führe zuerst 'python simple_train.py' aus.")
        return
    
    # Backtest durchführen
    print(f"\n🏃 Führe ML-Backtest durch...")
    
    initial_capital = 10000
    capital = initial_capital
    position = 0  # 0=cash, 1=long
    shares = 0
    portfolio_values = []
    trades = []
    signals = []
    predictions = []
    
    # Starte ab Index 50 (genug Daten für Features)
    for i in range(50, len(df)):
        current_date = df.iloc[i]['Date']
        current_price = df.iloc[i]['Close']
        
        # Signal generieren
        signal, prediction = strategy.generate_signal(df.iloc[:i+1], current_date)
        signals.append(signal)
        predictions.append(prediction)
        
        # Portfolio-Wert berechnen
        if position == 1:  # Long Position
            portfolio_value = shares * current_price
        else:  # Cash
            portfolio_value = capital
        
        portfolio_values.append(portfolio_value)
        
        # Trading-Logik
        if signal == 1 and position == 0:  # Buy Signal
            shares = capital / current_price * 0.999  # 0.1% Transaction Cost
            capital = 0
            position = 1
            trades.append({
                'Date': current_date,
                'Action': 'BUY',
                'Price': current_price,
                'Shares': shares,
                'Prediction': prediction
            })
            
        elif signal == -1 and position == 1:  # Sell Signal
            capital = shares * current_price * 0.999  # 0.1% Transaction Cost
            trades.append({
                'Date': current_date,
                'Action': 'SELL',
                'Price': current_price,
                'Shares': shares,
                'Prediction': prediction,
                'Profit': capital - initial_capital
            })
            shares = 0
            position = 0
    
    # Finale Werte
    final_price = df.iloc[-1]['Close']
    if position == 1:
        final_value = shares * final_price
    else:
        final_value = capital
    
    # Buy & Hold Benchmark
    buy_hold_shares = initial_capital / df.iloc[50]['Close']
    buy_hold_final = buy_hold_shares * final_price
    
    # Ergebnisse
    strategy_return = (final_value - initial_capital) / initial_capital * 100
    buy_hold_return = (buy_hold_final - initial_capital) / initial_capital * 100
    
    print(f"\n" + "="*60)
    print(f"📊 ML-STRATEGIE BACKTEST ERGEBNISSE")
    print(f"="*60)
    print(f"Startkapital:              ${initial_capital:>10,.2f}")
    print(f"Endwert Strategie:         ${final_value:>10,.2f}")
    print(f"Endwert Buy & Hold:        ${buy_hold_final:>10,.2f}")
    print(f"")
    print(f"Strategie Return:          {strategy_return:>10.2f}%")
    print(f"Buy & Hold Return:         {buy_hold_return:>10.2f}%")
    print(f"Alpha (Outperformance):    {strategy_return - buy_hold_return:>10.2f}%")
    print(f"")
    print(f"Anzahl Trades:             {len(trades):>10}")
    print(f"Durchschn. Prediction:     {np.mean(predictions):>10.3f}")
    print(f"Confidence Threshold:      {strategy.confidence_threshold:>10.2f}")
    print(f"Min Hold Days:             {strategy.min_hold_days:>10}")
    print(f"="*60)
    
    # Trade-Details
    if trades:
        print(f"\n📋 Trade-Details (letzte 10):")
        for trade in trades[-10:]:
            action = trade['Action']
            date = trade['Date'].strftime('%Y-%m-%d')
            price = trade['Price']
            pred = trade['Prediction']
            profit = trade.get('Profit', 0)
            print(f"   {date} | {action:4s} | ${price:7.2f} | Pred: {pred:.3f} | P&L: ${profit:8.2f}")
    
    # Signal-Statistiken
    signal_counts = pd.Series(signals).value_counts()
    print(f"\n📊 Signal-Verteilung:")
    print(f"   Hold (0):  {signal_counts.get(0, 0):4d} ({signal_counts.get(0, 0)/len(signals)*100:.1f}%)")
    print(f"   Buy (1):   {signal_counts.get(1, 0):4d} ({signal_counts.get(1, 0)/len(signals)*100:.1f}%)")
    print(f"   Sell (-1): {signal_counts.get(-1, 0):4d} ({signal_counts.get(-1, 0)/len(signals)*100:.1f}%)")
    
    print(f"\n🎉 ML-Backtest abgeschlossen!")
    
    return {
        'strategy_return': strategy_return,
        'buy_hold_return': buy_hold_return,
        'final_value': final_value,
        'trades': trades,
        'signals': signals,
        'predictions': predictions
    }

if __name__ == "__main__":
    run_ml_backtest()
