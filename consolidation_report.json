{"timestamp": "1753129002.919323", "consolidation_results": {"Remove duplicate directories": {"status": "success", "details": ["services/api-gateway"]}, "Consolidate monitoring configs": {"status": "error", "error": "[WinError 5] Zugriff verweigert: 'D:\\\\V5_Trader\\\\monitoring\\\\prometheus.yml'"}, "Organize documentation": {"status": "success", "details": ["Moved README.md to docs/00_README.md", "Moved SYSTEM_GUIDE.md to docs/02_System_Architecture.md", "Moved MONITORING_INTEGRATION_GUIDE.md to docs/03_Monitoring.md", "Moved DASHBOARD_CONSOLIDATION.md to docs/04_Dashboard.md", "Moved IMPROVEMENTS_SUMMARY.md to docs/05_Improvements.md", "Moved FINAL_SUMMARY.md to docs/06_Summary.md", "Set README_STREAMLINED.md as main README.md"]}, "Move utility scripts": {"status": "success", "details": ["Moved debug_data.py to scripts/", "Moved debug_target_calc.py to scripts/", "Moved debug_validation.py to scripts/", "Moved debug_yfinance.py to scripts/", "Moved check_data_size.py to scripts/", "Moved verify_target.py to scripts/", "Moved script.py to scripts/", "Moved chart_script.py to scripts/"]}, "Clean up root directory": {"status": "success", "details": ["Moved test_complete_system.py to tests/", "Moved test_dashboard_consolidation.py to tests/", "Moved test_dm.py to tests/", "Moved test_imports.py to tests/", "Moved test_monitoring_integration.py to tests/", "Moved test_postgres_connection.py to tests/", "Moved test_trading_strategy.py to tests/", "Removed __pycache__"]}, "Update references": {"status": "success", "details": ["Updated references in docker-compose.yml"]}, "Validate structure": {"status": "success", "details": {"required_directories": ["✅ config", "✅ data", "✅ models", "✅ services/api_gateway", "✅ monitoring/prometheus", "✅ docs", "✅ scripts", "✅ tests", "✅ backtesting", "✅ k8s"], "required_files": ["✅ README.md", "✅ requirements.txt", "✅ docker-compose.yml", "✅ .env.example", "✅ config/model_config.yaml", "✅ services/api_gateway/main.py", "✅ docs/01_Installation.md"], "issues": []}}}, "changes_log": ["Removed duplicate services/api-gateway directory", "Consolidated monitoring directory structure", "Organized documentation: README.md -> docs/00_README.md", "Organized documentation: SYSTEM_GUIDE.md -> docs/02_System_Architecture.md", "Organized documentation: MONITORING_INTEGRATION_GUIDE.md -> docs/03_Monitoring.md", "Organized documentation: DASHBOARD_CONSOLIDATION.md -> docs/04_Dashboard.md", "Organized documentation: IMPROVEMENTS_SUMMARY.md -> docs/05_Improvements.md", "Organized documentation: FINAL_SUMMARY.md -> docs/06_Summary.md", "Updated main README to streamlined version", "Organized script: debug_data.py", "Organized script: debug_target_calc.py", "Organized script: debug_validation.py", "Organized script: debug_yfinance.py", "Organized script: check_data_size.py", "Organized script: verify_target.py", "Organized script: script.py", "Organized script: chart_script.py", "Updated file references in docker-compose.yml"], "backup_location": "D:\\V5_Trader\\backups\\consolidation_backup"}