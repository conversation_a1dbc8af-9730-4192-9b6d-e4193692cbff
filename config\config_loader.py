#!/usr/bin/env python3
"""
Configuration loader for V5_Trader system
Centralized configuration management with validation
"""
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ModelConfig:
    """Model configuration dataclass"""
    name: str
    version: str
    input_features: int
    sequence_length: int
    d_model: int
    n_heads: int
    n_encoder_layers: int
    dropout: float
    cnn_filters: int
    lstm_units: int
    output_size: int
    learning_rate: float
    weight_decay: float
    batch_size: int
    max_epochs: int
    patience: int
    min_delta: float
    use_mixed_precision: bool
    uncertainty_estimation: bool
    ensemble_size: int
    gradient_clip_val: float
    val_split: float
    test_split: float
    sequence_overlap: float
    normalize: bool

@dataclass
class MLflowConfig:
    """MLflow configuration dataclass"""
    tracking_uri: str
    experiment_name: str
    run_name_prefix: str
    log_params: bool
    log_metrics: bool
    log_artifacts: bool
    log_model: bool

@dataclass
class EnvironmentConfig:
    """Environment configuration dataclass"""
    device: str
    num_workers: int
    pin_memory: bool
    persistent_workers: bool

@dataclass
class PathsConfig:
    """Paths configuration dataclass"""
    data_dir: str
    model_dir: str
    log_dir: str
    checkpoint_dir: str

class ConfigLoader:
    """Configuration loader and validator"""
    
    def __init__(self, config_path: Optional[str] = None):
        if config_path is None:
            config_path = Path(__file__).parent / "model_config.yaml"
        
        self.config_path = Path(config_path)
        self._config = None
        self._load_config()
    
    def _load_config(self):
        """Load configuration from YAML file"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)
            logger.info(f"✅ Configuration loaded from {self.config_path}")
        except FileNotFoundError:
            logger.error(f"❌ Configuration file not found: {self.config_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"❌ Error parsing YAML configuration: {e}")
            raise
    
    def get_model_config(self) -> ModelConfig:
        """Get model configuration as dataclass"""
        model_cfg = self._config['model']
        arch_cfg = model_cfg['architecture']
        train_cfg = model_cfg['training']
        feat_cfg = model_cfg['features']
        data_cfg = model_cfg['data']
        
        return ModelConfig(
            name=model_cfg['name'],
            version=model_cfg['version'],
            input_features=arch_cfg['input_features'],
            sequence_length=arch_cfg['sequence_length'],
            d_model=arch_cfg['d_model'],
            n_heads=arch_cfg['n_heads'],
            n_encoder_layers=arch_cfg['n_encoder_layers'],
            dropout=arch_cfg['dropout'],
            cnn_filters=arch_cfg['cnn_filters'],
            lstm_units=arch_cfg['lstm_units'],
            output_size=arch_cfg['output_size'],
            learning_rate=train_cfg['learning_rate'],
            weight_decay=train_cfg['weight_decay'],
            batch_size=train_cfg['batch_size'],
            max_epochs=train_cfg['max_epochs'],
            patience=train_cfg['patience'],
            min_delta=train_cfg['min_delta'],
            use_mixed_precision=feat_cfg['use_mixed_precision'],
            uncertainty_estimation=feat_cfg['uncertainty_estimation'],
            ensemble_size=feat_cfg['ensemble_size'],
            gradient_clip_val=feat_cfg['gradient_clip_val'],
            val_split=data_cfg['val_split'],
            test_split=data_cfg['test_split'],
            sequence_overlap=data_cfg['sequence_overlap'],
            normalize=data_cfg['normalize']
        )
    
    def get_mlflow_config(self) -> MLflowConfig:
        """Get MLflow configuration as dataclass"""
        mlflow_cfg = self._config['mlflow']
        logging_cfg = mlflow_cfg['logging']
        
        return MLflowConfig(
            tracking_uri=mlflow_cfg['tracking_uri'],
            experiment_name=mlflow_cfg['experiment_name'],
            run_name_prefix=mlflow_cfg['run_name_prefix'],
            log_params=logging_cfg['log_params'],
            log_metrics=logging_cfg['log_metrics'],
            log_artifacts=logging_cfg['log_artifacts'],
            log_model=logging_cfg['log_model']
        )
    
    def get_environment_config(self) -> EnvironmentConfig:
        """Get environment configuration as dataclass"""
        env_cfg = self._config['environment']
        
        return EnvironmentConfig(
            device=env_cfg['device'],
            num_workers=env_cfg['num_workers'],
            pin_memory=env_cfg['pin_memory'],
            persistent_workers=env_cfg['persistent_workers']
        )
    
    def get_paths_config(self) -> PathsConfig:
        """Get paths configuration as dataclass"""
        paths_cfg = self._config['paths']
        
        return PathsConfig(
            data_dir=paths_cfg['data_dir'],
            model_dir=paths_cfg['model_dir'],
            log_dir=paths_cfg['log_dir'],
            checkpoint_dir=paths_cfg['checkpoint_dir']
        )
    
    def get_raw_config(self) -> Dict[str, Any]:
        """Get raw configuration dictionary"""
        return self._config.copy()
    
    def validate_config(self) -> bool:
        """Validate configuration parameters"""
        try:
            model_cfg = self.get_model_config()
            mlflow_cfg = self.get_mlflow_config()
            env_cfg = self.get_environment_config()
            paths_cfg = self.get_paths_config()
            
            # Validate model parameters
            assert model_cfg.input_features > 0, "input_features must be positive"
            assert model_cfg.sequence_length > 0, "sequence_length must be positive"
            assert model_cfg.d_model > 0, "d_model must be positive"
            assert model_cfg.n_heads > 0, "n_heads must be positive"
            assert model_cfg.n_encoder_layers > 0, "n_encoder_layers must be positive"
            assert 0 < model_cfg.dropout < 1, "dropout must be between 0 and 1"
            assert model_cfg.learning_rate > 0, "learning_rate must be positive"
            assert model_cfg.batch_size > 0, "batch_size must be positive"
            assert model_cfg.max_epochs > 0, "max_epochs must be positive"
            assert 0 < model_cfg.val_split < 1, "val_split must be between 0 and 1"
            assert 0 < model_cfg.test_split < 1, "test_split must be between 0 and 1"
            assert model_cfg.val_split + model_cfg.test_split < 1, "val_split + test_split must be < 1"
            
            # Validate environment parameters
            assert env_cfg.device in ['auto', 'cpu', 'cuda'], "device must be 'auto', 'cpu', or 'cuda'"
            assert env_cfg.num_workers >= 0, "num_workers must be non-negative"
            
            # Validate paths exist or can be created
            for path_name, path_value in paths_cfg.__dict__.items():
                path_obj = Path(path_value)
                try:
                    path_obj.mkdir(parents=True, exist_ok=True)
                except Exception as e:
                    logger.warning(f"Cannot create directory {path_value}: {e}")
            
            logger.info("✅ Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Configuration validation failed: {e}")
            return False
    
    def update_config(self, updates: Dict[str, Any]):
        """Update configuration with new values"""
        def deep_update(base_dict, update_dict):
            for key, value in update_dict.items():
                if isinstance(value, dict) and key in base_dict:
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        deep_update(self._config, updates)
        logger.info("Configuration updated")
    
    def save_config(self, output_path: Optional[str] = None):
        """Save current configuration to file"""
        if output_path is None:
            output_path = self.config_path
        
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(self._config, f, default_flow_style=False, indent=2)
        
        logger.info(f"Configuration saved to {output_path}")

# Global configuration instance
_config_loader = None

def get_config_loader(config_path: Optional[str] = None) -> ConfigLoader:
    """Get global configuration loader instance"""
    global _config_loader
    if _config_loader is None:
        _config_loader = ConfigLoader(config_path)
    return _config_loader

def get_model_config() -> ModelConfig:
    """Convenience function to get model configuration"""
    return get_config_loader().get_model_config()

def get_mlflow_config() -> MLflowConfig:
    """Convenience function to get MLflow configuration"""
    return get_config_loader().get_mlflow_config()

def get_environment_config() -> EnvironmentConfig:
    """Convenience function to get environment configuration"""
    return get_config_loader().get_environment_config()

def get_paths_config() -> PathsConfig:
    """Convenience function to get paths configuration"""
    return get_config_loader().get_paths_config()

if __name__ == "__main__":
    # Test configuration loading
    loader = ConfigLoader()
    
    print("🧪 Testing configuration loading...")
    
    # Test validation
    if loader.validate_config():
        print("✅ Configuration is valid")
        
        # Print configurations
        model_cfg = loader.get_model_config()
        print(f"📊 Model: {model_cfg.name} v{model_cfg.version}")
        print(f"🏗️ Architecture: {model_cfg.d_model}d, {model_cfg.n_heads}h, {model_cfg.n_encoder_layers}l")
        print(f"🎯 Training: lr={model_cfg.learning_rate}, batch={model_cfg.batch_size}")
        
        mlflow_cfg = loader.get_mlflow_config()
        print(f"🧪 MLflow: {mlflow_cfg.experiment_name}")
        
    else:
        print("❌ Configuration validation failed")
