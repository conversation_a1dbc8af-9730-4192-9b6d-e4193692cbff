#!/usr/bin/env python3
"""
Wartungs-Tools für V5 Trader System
Automatisierte Backup-, Cleanup- und Health-Check-Funktionen
"""
import os
import subprocess
import shutil
import datetime
import json
import sys
from pathlib import Path
from typing import Dict, List, Tuple

class V5TraderMaintenance:
    """Wartungs-Tools für V5 Trader System"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.absolute()
        self.backup_dir = self.project_root / "backups"
        self.backup_dir.mkdir(exist_ok=True)
        self.logs_dir = self.project_root / "logs"
        self.logs_dir.mkdir(exist_ok=True)
    
    def backup_system(self) -> Path:
        """Erstellt System-Backup"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"v5trader_backup_{timestamp}"
        backup_path = self.backup_dir / backup_name
        
        print(f"💾 Erstelle System-Backup: {backup_name}")
        
        # Wichtige Verzeichnisse kopieren
        important_dirs = [
            "services", "models", "strategies", "tools", 
            "tests", "data", "static"
        ]
        
        backup_path.mkdir()
        
        for dir_name in important_dirs:
            src = self.project_root / dir_name
            if src.exists():
                dst = backup_path / dir_name
                try:
                    shutil.copytree(src, dst, ignore=shutil.ignore_patterns(
                        '__pycache__', '*.pyc', '*.pyo', '.git', 'venv'
                    ))
                    print(f"  ✅ {dir_name}")
                except Exception as e:
                    print(f"  ⚠️ {dir_name}: {e}")
        
        # Wichtige Dateien kopieren
        important_files = [
            "requirements.txt", "README.md", "SYSTEM_GUIDE.md",
            "start_v5trader.py", "train_and_register.py", 
            "fetch_market_data.py", "test_complete_system.py"
        ]
        
        for file_name in important_files:
            src = self.project_root / file_name
            if src.exists():
                dst = backup_path / file_name
                try:
                    shutil.copy2(src, dst)
                    print(f"  ✅ {file_name}")
                except Exception as e:
                    print(f"  ⚠️ {file_name}: {e}")
        
        # Backup-Info erstellen
        backup_info = {
            "timestamp": timestamp,
            "backup_name": backup_name,
            "python_version": sys.version,
            "system_info": {
                "platform": sys.platform,
                "cwd": str(self.project_root)
            }
        }
        
        with open(backup_path / "backup_info.json", "w") as f:
            json.dump(backup_info, f, indent=2)
        
        print(f"💾 Backup erstellt: {backup_path}")
        return backup_path
    
    def cleanup_logs(self, days_to_keep: int = 7) -> int:
        """Räumt alte Log-Dateien auf"""
        print(f"🧹 Räume Log-Dateien auf (behalte {days_to_keep} Tage)")
        
        if not self.logs_dir.exists():
            print("  ℹ️ Keine Log-Dateien gefunden")
            return 0
        
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_to_keep)
        deleted_count = 0
        total_size = 0
        
        for log_file in self.logs_dir.glob("*.log"):
            try:
                file_time = datetime.datetime.fromtimestamp(log_file.stat().st_mtime)
                if file_time < cutoff_date:
                    file_size = log_file.stat().st_size
                    total_size += file_size
                    log_file.unlink()
                    deleted_count += 1
                    print(f"  🗑️ Gelöscht: {log_file.name} ({file_size} bytes)")
            except Exception as e:
                print(f"  ⚠️ Fehler bei {log_file.name}: {e}")
        
        print(f"🧹 {deleted_count} alte Log-Dateien gelöscht ({total_size} bytes)")
        return deleted_count
    
    def cleanup_backups(self, backups_to_keep: int = 5) -> int:
        """Räumt alte Backups auf"""
        print(f"🧹 Räume alte Backups auf (behalte {backups_to_keep} neueste)")
        
        backup_dirs = [d for d in self.backup_dir.iterdir() if d.is_dir()]
        backup_dirs.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        deleted_count = 0
        for backup_dir in backup_dirs[backups_to_keep:]:
            try:
                shutil.rmtree(backup_dir)
                deleted_count += 1
                print(f"  🗑️ Gelöscht: {backup_dir.name}")
            except Exception as e:
                print(f"  ⚠️ Fehler bei {backup_dir.name}: {e}")
        
        print(f"🧹 {deleted_count} alte Backups gelöscht")
        return deleted_count
    
    def update_dependencies(self) -> bool:
        """Aktualisiert Python-Dependencies"""
        print("📦 Aktualisiere Dependencies...")
        
        try:
            # Check if virtual environment exists
            if sys.platform == "win32":
                pip_cmd = str(self.project_root / "venv" / "Scripts" / "pip.exe")
            else:
                pip_cmd = str(self.project_root / "venv" / "bin" / "pip")
            
            if not Path(pip_cmd).exists():
                pip_cmd = "pip"  # Fallback to system pip
                print("  ℹ️ Using system pip")
            
            # Update pip first
            subprocess.run([pip_cmd, "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            print("  ✅ pip aktualisiert")
            
            # Update requirements
            if (self.project_root / "requirements.txt").exists():
                subprocess.run([pip_cmd, "install", "--upgrade", "-r", "requirements.txt"], 
                             check=True, capture_output=True)
                print("  ✅ Requirements aktualisiert")
            
            # Generate new requirements
            result = subprocess.run([pip_cmd, "freeze"], 
                                  capture_output=True, text=True, check=True)
            
            with open(self.project_root / "requirements_updated.txt", "w") as f:
                f.write(result.stdout)
            
            print("  📄 Neue requirements_updated.txt erstellt")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"  ❌ Fehler beim Aktualisieren: {e}")
            return False
        except Exception as e:
            print(f"  ❌ Unerwarteter Fehler: {e}")
            return False
    
    def health_check(self) -> Dict[str, bool]:
        """Führt System-Gesundheitsprüfung durch"""
        print("🏥 System-Gesundheitsprüfung...")
        
        checks = {
            "Python Environment": self.check_python_env(),
            "Project Structure": self.check_project_structure(),
            "Dependencies": self.check_dependencies(),
            "Data Files": self.check_data_files(),
            "Disk Space": self.check_disk_space(),
            "WebSocket Port": self.check_websocket_port(),
        }
        
        all_healthy = True
        for check_name, status in checks.items():
            if status:
                print(f"  ✅ {check_name}")
            else:
                print(f"  ❌ {check_name}")
                all_healthy = False
        
        if all_healthy:
            print("🏥 System ist gesund!")
        else:
            print("⚠️ System benötigt Aufmerksamkeit!")
        
        return checks
    
    def check_python_env(self) -> bool:
        """Check Python environment"""
        try:
            version = sys.version_info
            return version.major == 3 and version.minor >= 8
        except:
            return False
    
    def check_project_structure(self) -> bool:
        """Check if all required directories exist"""
        required_dirs = ["models", "strategies", "services", "tools", "tests", "static"]
        return all((self.project_root / dir_name).exists() for dir_name in required_dirs)
    
    def check_dependencies(self) -> bool:
        """Check if key dependencies are available"""
        try:
            import torch
            import pandas
            import websockets
            return True
        except ImportError:
            return False
    
    def check_data_files(self) -> bool:
        """Check if data files exist"""
        data_file = self.project_root / "data" / "market_data.csv"
        return data_file.exists() and data_file.stat().st_size > 1000
    
    def check_disk_space(self) -> bool:
        """Prüfe verfügbaren Speicherplatz (>5GB erforderlich)"""
        try:
            free_bytes = shutil.disk_usage(self.project_root).free
            free_gb = free_bytes / (1024**3)
            return free_gb > 5
        except:
            return False
    
    def check_websocket_port(self) -> bool:
        """Check if WebSocket port is available"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', 8765))
            sock.close()
            # Port is available if connection fails (no server running)
            return result != 0
        except:
            return False
    
    def generate_system_report(self) -> Dict:
        """Generiert detaillierten Systembericht"""
        print("📋 Generiere Systembericht...")
        
        report = {
            "timestamp": datetime.datetime.now().isoformat(),
            "system_info": {
                "python_version": sys.version,
                "platform": sys.platform,
                "project_root": str(self.project_root)
            },
            "health_checks": self.health_check(),
            "file_counts": self.count_files(),
            "disk_usage": self.get_disk_usage()
        }
        
        report_file = self.project_root / f"system_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, "w") as f:
            json.dump(report, f, indent=2)
        
        print(f"📋 Systembericht erstellt: {report_file}")
        return report
    
    def count_files(self) -> Dict[str, int]:
        """Zählt Dateien nach Typ"""
        counts = {"python": 0, "data": 0, "config": 0, "other": 0}
        
        for file_path in self.project_root.rglob("*"):
            if file_path.is_file():
                suffix = file_path.suffix.lower()
                if suffix == ".py":
                    counts["python"] += 1
                elif suffix in [".csv", ".json", ".pkl"]:
                    counts["data"] += 1
                elif suffix in [".yml", ".yaml", ".toml", ".ini"]:
                    counts["config"] += 1
                else:
                    counts["other"] += 1
        
        return counts
    
    def get_disk_usage(self) -> Dict[str, float]:
        """Ermittelt Speicherverbrauch"""
        try:
            usage = shutil.disk_usage(self.project_root)
            return {
                "total_gb": usage.total / (1024**3),
                "used_gb": (usage.total - usage.free) / (1024**3),
                "free_gb": usage.free / (1024**3)
            }
        except:
            return {"total_gb": 0, "used_gb": 0, "free_gb": 0}
    
    def run_full_maintenance(self) -> bool:
        """Führt vollständige Wartung durch"""
        print("🔧 Starte vollständige System-Wartung")
        print("=" * 60)
        
        success = True
        
        try:
            # Backup erstellen
            self.backup_system()
            
            # Logs aufräumen
            self.cleanup_logs()
            
            # Alte Backups aufräumen
            self.cleanup_backups()
            
            # Gesundheitsprüfung
            health_results = self.health_check()
            if not all(health_results.values()):
                success = False
            
            # Systembericht generieren
            self.generate_system_report()
            
        except Exception as e:
            print(f"❌ Wartungsfehler: {e}")
            success = False
        
        print("=" * 60)
        if success:
            print("✅ Wartung erfolgreich abgeschlossen!")
        else:
            print("⚠️ Wartung mit Warnungen abgeschlossen!")
        
        return success


def main():
    """Main entry point"""
    maintenance = V5TraderMaintenance()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "backup":
            maintenance.backup_system()
        elif command == "cleanup":
            maintenance.cleanup_logs()
            maintenance.cleanup_backups()
        elif command == "update":
            maintenance.update_dependencies()
        elif command == "health":
            maintenance.health_check()
        elif command == "report":
            maintenance.generate_system_report()
        elif command == "full":
            maintenance.run_full_maintenance()
        else:
            print("Verfügbare Kommandos:")
            print("  backup  - System-Backup erstellen")
            print("  cleanup - Logs und alte Backups aufräumen")
            print("  update  - Dependencies aktualisieren")
            print("  health  - Gesundheitsprüfung durchführen")
            print("  report  - Detaillierten Systembericht erstellen")
            print("  full    - Vollständige Wartung durchführen")
    else:
        maintenance.run_full_maintenance()


if __name__ == "__main__":
    main()
