import os
import time
from datetime import datetime
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.cuda.amp import autocast, GradScaler
import pytorch_lightning as pl
from torchmetrics import MeanSquaredError, MeanAbsoluteError
import mlflow
import mlflow.pytorch


class RealTimeInferenceEngine:
    """
    Ultra-Low Latency Inference Engine für Production Trading
    """
    
    def __init__(self, model_registry_name, mlflow_uri="http://localhost:5000"):
        self.mlflow_uri = mlflow_uri
        mlflow.set_tracking_uri(mlflow_uri)
        
        # Load Production Models
        self.models = self._load_production_ensemble(model_registry_name)
        
        # Feature Engineering Pipeline
        self.feature_engineer = AdvancedFeatureEngineering()
        
        # Caching für Features
        self.feature_cache = {}
        self.cache_ttl = 60  # Sekunden
        
        # Performance Tracking
        self.inference_times = []
        self.prediction_history = []
        
    def _load_production_ensemble(self, model_name):
        """Lädt Ensemble von Production Models"""
        client = mlflow.MlflowClient()
        models = []
        
        # Suche alle Production Models
        for mv in client.search_model_versions(f"name='{model_name}'"):
            if mv.current_stage == "Production":
                model_uri = f"models:/{model_name}/{mv.version}"
                model = mlflow.pytorch.load_model(model_uri)
                model.eval()
                if torch.cuda.is_available():
                    model = model.cuda()
                models.append(model)
        
        print(f"Loaded {len(models)} production models")
        return models
    
    @torch.inference_mode()
    def predict(self, raw_data, return_uncertainty=True, use_cache=True):
        """
        Ultra-schnelle Vorhersage mit Uncertainty Quantification
        """
        start_time = time.time()
        
        # Feature Engineering mit Caching
        cache_key = self._generate_cache_key(raw_data)
        
        if use_cache and cache_key in self.feature_cache:
            features, cache_time = self.feature_cache[cache_key]
            if time.time() - cache_time < self.cache_ttl:
                processed_features = features
            else:
                processed_features = self._process_features(raw_data)
                self.feature_cache[cache_key] = (processed_features, time.time())
        else:
            processed_features = self._process_features(raw_data)
            if use_cache:
                self.feature_cache[cache_key] = (processed_features, time.time())
        
        # Ensemble Predictions
        predictions = []
        uncertainties = []
        
        with torch.cuda.amp.autocast():
            for model in self.models:
                output = model(processed_features, return_uncertainty=return_uncertainty)
                predictions.append(output['short_term'])
                
                if return_uncertainty and 'epistemic_uncertainty' in output:
                    uncertainties.append(output['epistemic_uncertainty'])
        
        # Ensemble Aggregation
        ensemble_prediction = torch.stack(predictions).mean(dim=0)
        
        if uncertainties:
            # Epistemic Uncertainty (Model Disagreement)
            epistemic_uncertainty = torch.stack(predictions).std(dim=0)
            
            # Aleatoric Uncertainty (Data Uncertainty)
            aleatoric_uncertainty = torch.stack(uncertainties).mean(dim=0)
            
            # Total Uncertainty
            total_uncertainty = torch.sqrt(epistemic_uncertainty**2 + aleatoric_uncertainty**2)
        else:
            epistemic_uncertainty = torch.zeros_like(ensemble_prediction)
            aleatoric_uncertainty = torch.zeros_like(ensemble_prediction)
            total_uncertainty = torch.zeros_like(ensemble_prediction)
        
        inference_time = time.time() - start_time
        self.inference_times.append(inference_time)
        
        result = {
            'prediction': ensemble_prediction.cpu().numpy(),
            'epistemic_uncertainty': epistemic_uncertainty.cpu().numpy(),
            'aleatoric_uncertainty': aleatoric_uncertainty.cpu().numpy(), 
            'total_uncertainty': total_uncertainty.cpu().numpy(),
            'confidence_score': self._calculate_confidence_score(total_uncertainty),
            'inference_time_ms': inference_time * 1000,
            'model_count': len(self.models)
        }
        
        self.prediction_history.append({
            'timestamp': datetime.now(),
            'prediction': result['prediction'],
            'uncertainty': result['total_uncertainty'],
            'inference_time': inference_time
        })
        
        return result
    
    def _calculate_confidence_score(self, uncertainty):
        """Berechnet Confidence Score basierend auf Uncertainty"""
        # Invertiere Uncertainty für Confidence (weniger Uncertainty = mehr Confidence)
        confidence = torch.exp(-uncertainty.mean())
        return confidence.cpu().numpy()
    
    def get_performance_metrics(self):
        """Performance Metriken für Monitoring"""
        if not self.inference_times:
            return {}
            
        return {
            'avg_inference_time_ms': np.mean(self.inference_times) * 1000,
            'p95_inference_time_ms': np.percentile(self.inference_times, 95) * 1000,
            'p99_inference_time_ms': np.percentile(self.inference_times, 99) * 1000,
            'total_predictions': len(self.inference_times),
            'cache_hit_rate': len(self.feature_cache) / max(len(self.inference_times), 1)
        }
