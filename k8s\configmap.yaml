apiVersion: v1
kind: ConfigMap
metadata:
  name: v5trader-config
  namespace: v5trader
data:
  # Application Configuration
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  
  # Database Configuration
  POSTGRES_DB: "v5trader"
  POSTGRES_USER: "postgres"
  POSTGRES_HOST: "postgres-service"
  POSTGRES_PORT: "5432"
  
  # Redis Configuration
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "0"
  
  # MLflow Configuration
  MLFLOW_TRACKING_URI: "http://mlflow-service:5000"
  MLFLOW_EXPERIMENT_NAME: "V5Trader_Production"
  MLFLOW_ARTIFACT_ROOT: "/mlflow/artifacts"
  
  # API Configuration
  API_HOST: "0.0.0.0"
  API_PORT: "8000"
  API_WORKERS: "4"
  
  # WebSocket Configuration
  WEBSOCKET_HOST: "0.0.0.0"
  WEBSOCKET_PORT: "8765"
  
  # Monitoring Configuration
  PROMETHEUS_PORT: "9090"
  GRAFANA_PORT: "3000"
  
  # Model Configuration
  MODEL_BATCH_SIZE: "32"
  MODEL_SEQUENCE_LENGTH: "60"
  MODEL_D_MODEL: "512"
  MODEL_N_HEADS: "8"
  
  # Trading Configuration
  TRADING_ENABLED: "false"  # Disable live trading in production by default
  PAPER_TRADING: "true"
  
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: v5trader
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
      - "alert_rules.yml"
    
    scrape_configs:
      - job_name: 'v5trader-api-gateway'
        static_configs:
          - targets: ['api-gateway-service:8000']
        metrics_path: '/metrics'
        scrape_interval: 10s
        
      - job_name: 'v5trader-websocket'
        static_configs:
          - targets: ['websocket-service:8765']
        metrics_path: '/metrics'
        scrape_interval: 10s
        
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - v5trader
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
            
  alert_rules.yml: |
    groups:
      - name: v5trader_alerts
        rules:
          - alert: HighCPUUsage
            expr: cpu_usage_percent > 80
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High CPU usage detected"
              description: "CPU usage is above 80% for more than 5 minutes"
              
          - alert: HighMemoryUsage
            expr: memory_usage_percent > 90
            for: 5m
            labels:
              severity: critical
            annotations:
              summary: "High memory usage detected"
              description: "Memory usage is above 90% for more than 5 minutes"
              
          - alert: APIGatewayDown
            expr: up{job="v5trader-api-gateway"} == 0
            for: 1m
            labels:
              severity: critical
            annotations:
              summary: "API Gateway is down"
              description: "V5Trader API Gateway has been down for more than 1 minute"
              
          - alert: ModelInferenceLatency
            expr: model_inference_duration_seconds > 0.1
            for: 2m
            labels:
              severity: warning
            annotations:
              summary: "High model inference latency"
              description: "Model inference is taking longer than 100ms"
