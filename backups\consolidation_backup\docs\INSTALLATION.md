# 🛠️ V5_Trader Installation Guide

## Systemvoraussetzungen

### Hardware
- **CPU**: Intel i5/AMD Ryzen 5 oder besser
- **RAM**: 8GB (16GB empfohlen)
- **Storage**: 50GB freier Speicherplatz
- **GPU**: Optional (NVIDIA mit CUDA für ML-Training)

### Software
- **OS**: Windows 10/11 (64-bit), Linux, macOS
- **Python**: 3.8+ (3.11+ empfohlen)
- **Git**: Neueste Version
- **Browser**: Chrome, Firefox, Edge, Safari

## 🚀 Schnellinstallation

### 1. Repository klonen
```bash
git clone https://github.com/username/v5trader.git
cd v5trader
```

### 2. Virtual Environment erstellen
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate
```

### 3. Dependencies installieren
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

### 4. Daten laden
```bash
python fetch_market_data.py
```

### 5. System starten
```bash
python start_v5trader.py
```

## 📋 Detaillierte Installation

### Schritt 1: Python Installation

#### Windows
1. Download von [python.org](https://python.org)
2. **Wichtig**: "Add Python to PATH" aktivieren
3. Installation mit Administrator-Rechten

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install python3.11 python3.11-venv python3.11-dev
```

#### macOS
```bash
# Mit Homebrew
brew install python@3.11
```

### Schritt 2: Git Installation

#### Windows
- Download von [git-scm.com](https://git-scm.com)
- Standard-Einstellungen verwenden

#### Linux
```bash
sudo apt install git
```

#### macOS
```bash
brew install git
```

### Schritt 3: Projekt Setup

```bash
# Repository klonen
git clone https://github.com/username/v5trader.git
cd v5trader

# Virtual Environment
python -m venv venv

# Aktivieren (Windows)
venv\Scripts\activate

# Aktivieren (Linux/macOS)
source venv/bin/activate

# Dependencies
pip install --upgrade pip setuptools wheel
pip install -r requirements.txt
```

### Schritt 4: Konfiguration

#### Environment Variables (optional)
```bash
# .env Datei erstellen
cp .env.template .env

# Anpassen nach Bedarf
ALPHA_VANTAGE_API_KEY=your_key_here
POSTGRES_PASSWORD=your_password
```

#### Verzeichnisse erstellen
```bash
mkdir -p logs backups data/raw data/processed
```

### Schritt 5: Erste Tests

```bash
# System-Validierung
python test_complete_system.py

# Komponenten-Tests
python run_tests.py

# Dashboard-Test
python test_dashboard.py
```

## 🐳 Docker Installation (Optional)

### Docker Desktop installieren
1. Download von [docker.com](https://docker.com)
2. Installation und Neustart
3. Docker Desktop starten

### Services starten
```bash
# Alle Services
docker-compose up -d

# Einzelne Services
docker-compose up -d postgres redis grafana
```

## 🔧 Entwicklungsumgebung

### VS Code Setup
```bash
# Extensions installieren
code --install-extension ms-python.python
code --install-extension ms-python.pylint
code --install-extension ms-python.black-formatter
```

### Code Quality Tools
```bash
# Linting und Formatting
pip install black isort mypy pylint

# Pre-commit hooks
pip install pre-commit
pre-commit install
```

## 🧪 Verifikation

### System-Tests ausführen
```bash
# Vollständige Validierung
python test_complete_system.py

# Einzelne Komponenten
python test_trading_strategy.py
python tests/test_data_pipeline.py
python tests/test_model_integration.py
```

### Services testen
```bash
# WebSocket Server
python services/websocket_server.py

# System Monitor
python tools/system_monitor.py

# Dashboard
python test_dashboard.py
```

## 🚨 Troubleshooting

### Häufige Probleme

#### Python nicht gefunden
```bash
# Windows: Python zu PATH hinzufügen
# Oder vollständigen Pfad verwenden
C:\Python311\python.exe -m venv venv
```

#### Permission Errors (Linux/macOS)
```bash
# Berechtigungen setzen
chmod +x start_v5trader.py
sudo chown -R $USER:$USER /path/to/v5trader
```

#### Port bereits belegt
```bash
# Prozess finden und beenden
netstat -ano | findstr :8765
taskkill /PID <PID> /F
```

#### Dependencies Fehler
```bash
# Cache leeren und neu installieren
pip cache purge
pip install --no-cache-dir -r requirements.txt
```

#### WebSocket Verbindung fehlschlägt
```bash
# Firewall prüfen
# Windows: Windows Defender Firewall
# Linux: ufw status
# Antivirus-Software prüfen
```

### Logs prüfen
```bash
# System-Logs
tail -f logs/system.log

# Python-Logs
python -c "import logging; logging.basicConfig(level=logging.DEBUG)"
```

## 🔄 Updates

### System aktualisieren
```bash
# Git Updates
git pull origin main

# Dependencies aktualisieren
pip install --upgrade -r requirements.txt

# Wartung ausführen
python tools/maintenance.py update
```

### Backup vor Updates
```bash
# Automatisches Backup
python tools/maintenance.py backup

# Manuelles Backup
cp -r v5trader v5trader_backup_$(date +%Y%m%d)
```

## 🌐 Produktionsdeployment

### Server-Konfiguration
```bash
# Systemd Service (Linux)
sudo cp scripts/v5trader.service /etc/systemd/system/
sudo systemctl enable v5trader
sudo systemctl start v5trader
```

### Reverse Proxy (Nginx)
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /ws {
        proxy_pass http://localhost:8765;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

### SSL/TLS (Let's Encrypt)
```bash
# Certbot installieren
sudo apt install certbot python3-certbot-nginx

# Zertifikat erstellen
sudo certbot --nginx -d your-domain.com
```

## 📞 Support

### Hilfe erhalten
- **Dokumentation**: [docs/](../docs/)
- **Issues**: [GitHub Issues](https://github.com/username/v5trader/issues)
- **Diskussionen**: [GitHub Discussions](https://github.com/username/v5trader/discussions)

### Logs einreichen
```bash
# System-Report erstellen
python tools/maintenance.py report

# Logs sammeln
tar -czf v5trader_logs.tar.gz logs/ system_report_*.json
```

---

**Bei Problemen**: Erstelle ein GitHub Issue mit detaillierter Fehlerbeschreibung und System-Report.
