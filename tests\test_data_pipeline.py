"""
Comprehensive tests for the data pipeline
"""
import pytest
import pandas as pd
import torch
import os
import tempfile
from datetime import datetime, timedelta

# Add parent directory to path for imports
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.fin_data_module import FinancialDataModule, TimeSeriesDataset
import fetch_market_data


class TestDataPipeline:
    """Test suite for data pipeline components"""
    
    def setup_method(self):
        """Setup test data"""
        # Create sample data
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        self.sample_data = pd.DataFrame({
            'Date': dates,
            'Open': 100 + torch.randn(100).numpy() * 5,
            'High': 105 + torch.randn(100).numpy() * 5,
            'Low': 95 + torch.randn(100).numpy() * 5,
            'Close': 100 + torch.randn(100).numpy() * 5,
            'Volume': 1000000 + torch.randint(0, 500000, (100,)).numpy(),
            'Ticker': 'TEST'
        })
        
        # Add target column
        self.sample_data['Target'] = (
            self.sample_data['Close'].shift(-1) > self.sample_data['Close']
        ).astype(float)
        self.sample_data = self.sample_data.dropna()
        
    def test_timeseries_dataset_creation(self):
        """Test TimeSeriesDataset creation and basic functionality"""
        features = self.sample_data[['Open', 'High', 'Low', 'Close', 'Volume']]
        targets = self.sample_data['Target']
        seq_len = 10
        
        dataset = TimeSeriesDataset(features, targets, seq_len)
        
        # Test dataset properties
        assert len(dataset) == len(features) - seq_len
        assert dataset.features.shape == (len(features), 5)
        assert dataset.targets.shape == (len(targets),)
        assert dataset.seq_len == seq_len
        
        # Test data retrieval
        x, y = dataset[0]
        assert x.shape == (seq_len, 5)
        assert y.shape == ()
        assert isinstance(x, torch.Tensor)
        assert isinstance(y, torch.Tensor)
        
    def test_timeseries_dataset_numeric_conversion(self):
        """Test that non-numeric data is properly converted"""
        # Create data with object dtypes (like from CSV)
        features = pd.DataFrame({
            'Open': ['100.5', '101.0', '99.5'],
            'High': ['105.0', '106.0', '104.0'],
            'Low': ['95.0', '96.0', '94.0'],
            'Close': ['100.0', '101.0', '99.0'],
            'Volume': ['1000000', '1100000', '900000']
        })
        targets = pd.Series([1.0, 0.0, 1.0])
        
        dataset = TimeSeriesDataset(features, targets, seq_len=2)
        
        # Should have 1 sample (3 - 2 = 1)
        assert len(dataset) == 1
        x, y = dataset[0]
        assert x.shape == (2, 5)
        assert torch.all(torch.isfinite(x))  # All values should be finite numbers
        
    def test_financial_data_module(self):
        """Test FinancialDataModule functionality"""
        features = self.sample_data[['Open', 'High', 'Low', 'Close', 'Volume']]
        targets = self.sample_data['Target']
        
        dm = FinancialDataModule(
            features=features,
            targets=targets,
            seq_len=10,
            batch_size=8,
            val_split=0.2
        )
        
        dm.setup()
        
        # Test data loaders
        train_loader = dm.train_dataloader()
        val_loader = dm.val_dataloader()
        
        # Test batch shapes
        train_batch = next(iter(train_loader))
        val_batch = next(iter(val_loader))
        
        x_train, y_train = train_batch
        x_val, y_val = val_batch
        
        assert x_train.shape[1:] == (10, 5)  # seq_len, features
        assert x_val.shape[1:] == (10, 5)
        assert len(y_train.shape) == 1  # batch dimension only
        assert len(y_val.shape) == 1
        
    def test_fetch_market_data_structure(self):
        """Test market data fetching structure (without actual API call)"""
        # Test the data processing logic with mock data
        mock_data = pd.DataFrame({
            ('Date', ''): pd.date_range('2023-01-01', periods=5),
            ('Open', 'AAPL'): [100, 101, 102, 103, 104],
            ('High', 'AAPL'): [105, 106, 107, 108, 109],
            ('Low', 'AAPL'): [95, 96, 97, 98, 99],
            ('Close', 'AAPL'): [100, 101, 102, 103, 104],
            ('Volume', 'AAPL'): [1000000, 1100000, 1200000, 1300000, 1400000]
        })
        mock_data.columns = pd.MultiIndex.from_tuples(mock_data.columns)
        
        # Test column flattening logic
        if isinstance(mock_data.columns, pd.MultiIndex):
            new_columns = []
            for col in mock_data.columns:
                if col[0] == 'Date' or col[1] == '':
                    new_columns.append(col[0])
                else:
                    new_columns.append(col[0])
            mock_data.columns = new_columns
            
        expected_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        assert all(col in mock_data.columns for col in expected_columns)
        
    def test_target_calculation(self):
        """Test target calculation logic"""
        closes = pd.Series([100, 101, 99, 102, 98])

        # Calculate targets like in fetch_market_data
        shifted = closes.shift(-1)
        actual_targets = (shifted > closes).astype(float)

        # Expected: [1.0, 0.0, 1.0, 0.0, 0.0] (last comparison is NaN > 98 = False)
        expected_values = [1.0, 0.0, 1.0, 0.0, 0.0]
        actual_values = actual_targets.tolist()

        assert actual_values == expected_values, f"Expected {expected_values}, got {actual_values}"

        # Test that we properly handle the NaN case by dropping it in real usage
        # In fetch_market_data.py, we call dropna(subset=['Target']) to remove these
        targets_with_nan = pd.Series([1.0, 0.0, 1.0, 0.0, float('nan')])
        clean_targets = targets_with_nan.dropna()
        assert len(clean_targets) == 4
        
    def test_data_validation(self):
        """Test data validation and error handling"""
        # Test empty features
        try:
            TimeSeriesDataset(pd.DataFrame(), pd.Series([1, 2, 3]), 5)
            assert False, "Should have raised ValueError for empty DataFrame"
        except ValueError as e:
            assert "Features DataFrame is empty" in str(e)

        # Test no numeric features
        non_numeric_features = pd.DataFrame({
            'text_col': ['a', 'b', 'c'],
            'another_text': ['x', 'y', 'z']
        })
        try:
            TimeSeriesDataset(non_numeric_features, pd.Series([1, 2, 3]), 2)
            assert False, "Should have raised ValueError for no numeric features"
        except ValueError as e:
            assert "No numeric features found" in str(e)


if __name__ == "__main__":
    # Run tests
    test_suite = TestDataPipeline()
    test_suite.setup_method()
    
    print("🧪 Running data pipeline tests...")
    
    try:
        test_suite.test_timeseries_dataset_creation()
        print("✅ TimeSeriesDataset creation test passed")
        
        test_suite.test_timeseries_dataset_numeric_conversion()
        print("✅ Numeric conversion test passed")
        
        test_suite.test_financial_data_module()
        print("✅ FinancialDataModule test passed")
        
        test_suite.test_fetch_market_data_structure()
        print("✅ Market data structure test passed")
        
        test_suite.test_target_calculation()
        print("✅ Target calculation test passed")
        
        test_suite.test_data_validation()
        print("✅ Data validation test passed")
        
        print("\n🎉 All data pipeline tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise
