# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Machine Learning – PYTHON 3.13 KOMPATIBLE VERSIONEN
torch>=2.0.0
scikit-learn>=1.7.0    # Unterstützt Python 3.13
pandas>=2.0.0
numpy>=1.24.0
mlflow>=2.8.1
optuna>=3.4.0

# Data Processing – AKTUALISIERT
redis==5.0.1
psycopg2-binary==2.9.10   # WICHTIG: 2.9.10 unterstützt Python 3.13!
sqlalchemy>=2.0.26        # Bugfixes für Python 3.13
alembic==1.12.1

# API Clients
requests==2.31.0
websocket-client==1.6.4
alpha-vantage==2.3.1
yfinance>=0.0.2

# Async Processing – PYTHON 3.13 KOMPATIBEL
aiohttp>=3.12.0   # Version 3.12+ unterstützt Python 3.13
asyncio-mqtt==0.16.1

# Monitoring & Logging
prometheus-client==0.19.0
structlog==23.2.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Development
black==23.11.0
isort==5.12.0
mypy==1.7.1

# AI_Models
pytorch-lightning>=2.0.0
torchmetrics>=0.11.0
