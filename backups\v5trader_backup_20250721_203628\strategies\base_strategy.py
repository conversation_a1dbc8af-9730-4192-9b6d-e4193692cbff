from abc import ABC, abstractmethod
from typing import Dict, List, Optional
import pandas as pd
import numpy as np
from datetime import datetime
import logging

class BaseStrategy(ABC):
    """Basis-Klasse für alle Trading-Strategien"""
    
    def __init__(self, name: str, parameters: Dict):
        self.name = name
        self.parameters = parameters
        self.positions = {}
        self.balance = 100000  # Startkapital: 100k
        self.trades = []
        self.logger = logging.getLogger(f"Strategy.{name}")
        
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> Dict:
        """Generiert Trading-Signale basierend auf Marktdaten"""
        pass
    
    @abstractmethod
    def calculate_position_size(self, signal: Dict, current_price: float) -> float:
        """Berechnet die Positionsgröße"""
        pass
    
    def execute_trade(self, symbol: str, action: str, quantity: float, price: float):
        """Führt einen Trade aus"""
        timestamp = datetime.now()
        
        if action.upper() == "BUY":
            cost = quantity * price
            if cost <= self.balance:
                self.balance -= cost
                if symbol in self.positions:
                    self.positions[symbol] += quantity
                else:
                    self.positions[symbol] = quantity
                    
                trade = {
                    "timestamp": timestamp,
                    "symbol": symbol,
                    "action": "BUY",
                    "quantity": quantity,
                    "price": price,
                    "cost": cost
                }
                self.trades.append(trade)
                self.logger.info(f"BOUGHT {quantity} {symbol} @ {price}")
                
        elif action.upper() == "SELL":
            if symbol in self.positions and self.positions[symbol] >= quantity:
                revenue = quantity * price
                self.balance += revenue
                self.positions[symbol] -= quantity
                
                if self.positions[symbol] == 0:
                    del self.positions[symbol]
                
                trade = {
                    "timestamp": timestamp,
                    "symbol": symbol,
                    "action": "SELL",
                    "quantity": quantity,
                    "price": price,
                    "revenue": revenue
                }
                self.trades.append(trade)
                self.logger.info(f"SOLD {quantity} {symbol} @ {price}")
    
    def get_portfolio_value(self, current_prices: Dict) -> float:
        """Berechnet den aktuellen Portfolio-Wert"""
        portfolio_value = self.balance
        
        for symbol, quantity in self.positions.items():
            if symbol in current_prices:
                portfolio_value += quantity * current_prices[symbol]
                
        return portfolio_value
    
    def get_performance_metrics(self) -> Dict:
        """Berechnet Performance-Metriken"""
        if not self.trades:
            return {}
            
        df_trades = pd.DataFrame(self.trades)
        
        # Grundlegende Metriken
        total_trades = len(df_trades)
        buy_trades = len(df_trades[df_trades['action'] == 'BUY'])
        sell_trades = len(df_trades[df_trades['action'] == 'SELL'])
        
        return {
            "total_trades": total_trades,
            "buy_trades": buy_trades,
            "sell_trades": sell_trades,
            "current_balance": self.balance,
            "positions": self.positions.copy()
        }
