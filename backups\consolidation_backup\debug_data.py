import pandas as pd
import os

# Load and inspect the data
df_raw = pd.read_csv(os.path.join("data", "market_data.csv"), parse_dates=["Date"])
print("Raw data columns:", df_raw.columns.tolist())
print("Raw data dtypes:\n", df_raw.dtypes)
print("Raw data shape:", df_raw.shape)
print("First few rows:\n", df_raw.head())

# Filter AAPL data
df_aapl = df_raw[df_raw["Ticker"] == "AAPL"].copy()
df_aapl.set_index("Date", inplace=True)
print("\nAAPL data columns:", df_aapl.columns.tolist())
print("AAPL data dtypes:\n", df_aapl.dtypes)
print("AAPL data shape:", df_aapl.shape)

# Check feature columns
feature_cols = ["Open", "High", "Low", "Close", "Volume"]
print(f"\nRequested feature columns: {feature_cols}")
print("Available columns in AAPL data:", df_aapl.columns.tolist())

# Check if feature columns exist
missing_cols = [col for col in feature_cols if col not in df_aapl.columns]
if missing_cols:
    print(f"Missing columns: {missing_cols}")

# Try to select features
df_feats = df_aapl[feature_cols]
print(f"\nFeature dataframe shape: {df_feats.shape}")
print("Feature dataframe dtypes:\n", df_feats.dtypes)

# Check numeric selection
features_num = df_feats.select_dtypes(include=["number"])
print(f"\nNumeric features shape: {features_num.shape}")
print("Numeric features columns:", features_num.columns.tolist())
print("Numeric features dtypes:\n", features_num.dtypes)
