#!/usr/bin/env python3
"""
📈 V5_Trader - Backtesting Script
Führt einen einfachen Backtest mit den verfügbaren Daten durch
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def load_market_data():
    """Lädt die Marktdaten"""
    print("📊 Lade Marktdaten...")
    
    data_path = Path("data/market_data.csv")
    if not data_path.exists():
        print("❌ Marktdaten nicht gefunden!")
        return None
    
    df = pd.read_csv(data_path)
    df['Date'] = pd.to_datetime(df['Date'])
    df = df.sort_values('Date').reset_index(drop=True)
    
    print(f"✅ {len(df)} Datenpunkte geladen")
    print(f"📅 Zeitraum: {df['Date'].min().date()} bis {df['Date'].max().date()}")
    
    return df

def calculate_technical_indicators(df):
    """Berechnet technische Indikatoren"""
    print("🔧 Berechne technische Indikatoren...")
    
    # Returns
    df['Returns'] = df['Close'].pct_change()
    
    # Moving Averages
    df['SMA_5'] = df['Close'].rolling(5).mean()
    df['SMA_20'] = df['Close'].rolling(20).mean()
    df['SMA_50'] = df['Close'].rolling(50).mean()
    
    # Volatility
    df['Volatility'] = df['Returns'].rolling(20).std()
    
    # RSI
    delta = df['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['RSI'] = 100 - (100 / (1 + rs))
    
    # MACD
    ema_12 = df['Close'].ewm(span=12).mean()
    ema_26 = df['Close'].ewm(span=26).mean()
    df['MACD'] = ema_12 - ema_26
    df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
    
    # Bollinger Bands
    df['BB_Middle'] = df['Close'].rolling(20).mean()
    bb_std = df['Close'].rolling(20).std()
    df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
    df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)
    
    print("✅ Technische Indikatoren berechnet")
    return df

def simple_momentum_strategy(df):
    """Einfache Momentum-Strategie"""
    print("🎯 Implementiere Momentum-Strategie...")
    
    signals = []
    
    for i in range(len(df)):
        if i < 50:  # Brauchen mindestens 50 Datenpunkte
            signals.append(0)
            continue
        
        # Signal-Bedingungen
        price = df.iloc[i]['Close']
        sma_5 = df.iloc[i]['SMA_5']
        sma_20 = df.iloc[i]['SMA_20']
        sma_50 = df.iloc[i]['SMA_50']
        rsi = df.iloc[i]['RSI']
        macd = df.iloc[i]['MACD']
        macd_signal = df.iloc[i]['MACD_Signal']
        
        # Bullish Bedingungen
        bullish_conditions = [
            price > sma_5,           # Preis über kurzfristigem MA
            sma_5 > sma_20,          # Kurzfristiger MA über mittelfristigem MA
            sma_20 > sma_50,         # Mittelfristiger MA über langfristigem MA
            rsi > 30 and rsi < 70,   # RSI nicht überkauft/überverkauft
            macd > macd_signal       # MACD über Signal-Linie
        ]
        
        # Bearish Bedingungen
        bearish_conditions = [
            price < sma_5,           # Preis unter kurzfristigem MA
            sma_5 < sma_20,          # Kurzfristiger MA unter mittelfristigem MA
            sma_20 < sma_50,         # Mittelfristiger MA unter langfristigem MA
            rsi > 70 or rsi < 30,    # RSI überkauft oder überverkauft
            macd < macd_signal       # MACD unter Signal-Linie
        ]
        
        # Signal generieren
        if sum(bullish_conditions) >= 3:
            signals.append(1)   # Buy
        elif sum(bearish_conditions) >= 3:
            signals.append(-1)  # Sell
        else:
            signals.append(0)   # Hold
    
    df['Signal'] = signals
    print("✅ Momentum-Strategie implementiert")
    return df

def run_backtest(df, initial_capital=10000, transaction_cost=0.001):
    """Führt den Backtest durch"""
    print(f"🏃 Führe Backtest durch (Startkapital: ${initial_capital:,.2f})...")
    
    capital = initial_capital
    position = 0  # 0 = keine Position, 1 = long, -1 = short
    shares = 0
    portfolio_values = []
    trades = []
    
    for i in range(len(df)):
        current_price = df.iloc[i]['Close']
        signal = df.iloc[i]['Signal']
        date = df.iloc[i]['Date']
        
        # Portfolio-Wert berechnen
        if position == 1:  # Long Position
            portfolio_value = capital + (shares * current_price)
        elif position == -1:  # Short Position
            portfolio_value = capital - (shares * current_price)
        else:  # Keine Position
            portfolio_value = capital
        
        portfolio_values.append(portfolio_value)
        
        # Trading-Logik
        if signal == 1 and position != 1:  # Buy Signal
            if position == -1:  # Schließe Short Position
                capital += shares * current_price * (1 - transaction_cost)
                trades.append({
                    'Date': date,
                    'Action': 'Cover Short',
                    'Price': current_price,
                    'Shares': shares,
                    'Capital': capital
                })
                shares = 0
            
            # Öffne Long Position
            shares = capital / current_price * (1 - transaction_cost)
            capital = 0
            position = 1
            trades.append({
                'Date': date,
                'Action': 'Buy Long',
                'Price': current_price,
                'Shares': shares,
                'Capital': capital
            })
            
        elif signal == -1 and position != -1:  # Sell Signal
            if position == 1:  # Schließe Long Position
                capital = shares * current_price * (1 - transaction_cost)
                trades.append({
                    'Date': date,
                    'Action': 'Sell Long',
                    'Price': current_price,
                    'Shares': shares,
                    'Capital': capital
                })
                shares = 0
            
            # Öffne Short Position (vereinfacht)
            shares = capital / current_price * (1 - transaction_cost)
            position = -1
            trades.append({
                'Date': date,
                'Action': 'Sell Short',
                'Price': current_price,
                'Shares': shares,
                'Capital': capital
            })
    
    # Finale Portfolio-Werte
    df['Portfolio_Value'] = portfolio_values
    
    # Buy & Hold Benchmark
    initial_shares = initial_capital / df.iloc[0]['Close']
    df['Buy_Hold_Value'] = initial_shares * df['Close']
    
    print(f"✅ Backtest abgeschlossen ({len(trades)} Trades)")
    return df, trades

def calculate_performance_metrics(df, initial_capital):
    """Berechnet Performance-Metriken"""
    print("📊 Berechne Performance-Metriken...")
    
    final_value = df['Portfolio_Value'].iloc[-1]
    buy_hold_final = df['Buy_Hold_Value'].iloc[-1]
    
    # Returns
    strategy_return = (final_value - initial_capital) / initial_capital * 100
    buy_hold_return = (buy_hold_final - initial_capital) / initial_capital * 100
    
    # Portfolio Returns für weitere Metriken
    portfolio_returns = df['Portfolio_Value'].pct_change().dropna()
    buy_hold_returns = df['Buy_Hold_Value'].pct_change().dropna()
    
    # Sharpe Ratio (vereinfacht, ohne Risikofrei-Zins)
    strategy_sharpe = portfolio_returns.mean() / portfolio_returns.std() * np.sqrt(252)
    buy_hold_sharpe = buy_hold_returns.mean() / buy_hold_returns.std() * np.sqrt(252)
    
    # Maximum Drawdown
    strategy_peak = df['Portfolio_Value'].expanding().max()
    strategy_drawdown = (df['Portfolio_Value'] - strategy_peak) / strategy_peak
    max_drawdown = strategy_drawdown.min() * 100
    
    buy_hold_peak = df['Buy_Hold_Value'].expanding().max()
    buy_hold_drawdown = (df['Buy_Hold_Value'] - buy_hold_peak) / buy_hold_peak
    buy_hold_max_dd = buy_hold_drawdown.min() * 100
    
    # Volatility
    strategy_vol = portfolio_returns.std() * np.sqrt(252) * 100
    buy_hold_vol = buy_hold_returns.std() * np.sqrt(252) * 100
    
    metrics = {
        'Strategy Return (%)': strategy_return,
        'Buy & Hold Return (%)': buy_hold_return,
        'Strategy Sharpe Ratio': strategy_sharpe,
        'Buy & Hold Sharpe Ratio': buy_hold_sharpe,
        'Strategy Max Drawdown (%)': max_drawdown,
        'Buy & Hold Max Drawdown (%)': buy_hold_max_dd,
        'Strategy Volatility (%)': strategy_vol,
        'Buy & Hold Volatility (%)': buy_hold_vol,
        'Final Portfolio Value': final_value,
        'Final Buy & Hold Value': buy_hold_final
    }
    
    print("✅ Performance-Metriken berechnet")
    return metrics

def plot_results(df, metrics):
    """Erstellt Visualisierungen"""
    print("📈 Erstelle Visualisierungen...")
    
    plt.style.use('seaborn-v0_8')
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('V5_Trader Backtest Ergebnisse', fontsize=16, fontweight='bold')
    
    # Portfolio Performance
    axes[0, 0].plot(df['Date'], df['Portfolio_Value'], label='Strategy', linewidth=2)
    axes[0, 0].plot(df['Date'], df['Buy_Hold_Value'], label='Buy & Hold', linewidth=2)
    axes[0, 0].set_title('Portfolio Performance')
    axes[0, 0].set_ylabel('Portfolio Value ($)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Price und Signale
    axes[0, 1].plot(df['Date'], df['Close'], label='Price', alpha=0.7)
    buy_signals = df[df['Signal'] == 1]
    sell_signals = df[df['Signal'] == -1]
    axes[0, 1].scatter(buy_signals['Date'], buy_signals['Close'], 
                      color='green', marker='^', s=50, label='Buy Signal')
    axes[0, 1].scatter(sell_signals['Date'], sell_signals['Close'], 
                      color='red', marker='v', s=50, label='Sell Signal')
    axes[0, 1].set_title('Price & Trading Signals')
    axes[0, 1].set_ylabel('Price ($)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # Drawdown
    strategy_peak = df['Portfolio_Value'].expanding().max()
    strategy_drawdown = (df['Portfolio_Value'] - strategy_peak) / strategy_peak * 100
    axes[1, 0].fill_between(df['Date'], strategy_drawdown, 0, alpha=0.3, color='red')
    axes[1, 0].plot(df['Date'], strategy_drawdown, color='red', linewidth=1)
    axes[1, 0].set_title('Strategy Drawdown')
    axes[1, 0].set_ylabel('Drawdown (%)')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Performance Metriken
    axes[1, 1].axis('off')
    metrics_text = ""
    for key, value in metrics.items():
        if isinstance(value, float):
            if 'Return' in key or 'Drawdown' in key or 'Volatility' in key:
                metrics_text += f"{key}: {value:.2f}%\n"
            elif 'Ratio' in key:
                metrics_text += f"{key}: {value:.3f}\n"
            else:
                metrics_text += f"{key}: ${value:,.2f}\n"
        else:
            metrics_text += f"{key}: {value}\n"
    
    axes[1, 1].text(0.1, 0.9, metrics_text, transform=axes[1, 1].transAxes,
                   fontsize=10, verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    axes[1, 1].set_title('Performance Metrics')
    
    plt.tight_layout()
    plt.savefig('backtest_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ Visualisierungen erstellt und gespeichert")

def main():
    """Hauptfunktion"""
    print("🚀 V5_Trader Backtest gestartet\n")
    
    # Daten laden
    df = load_market_data()
    if df is None:
        return
    
    # Technische Indikatoren
    df = calculate_technical_indicators(df)
    
    # Strategie implementieren
    df = simple_momentum_strategy(df)
    
    # Backtest durchführen
    df, trades = run_backtest(df, initial_capital=10000)
    
    # Performance berechnen
    metrics = calculate_performance_metrics(df, 10000)
    
    # Ergebnisse anzeigen
    print("\n" + "="*60)
    print("📊 BACKTEST ERGEBNISSE")
    print("="*60)
    
    for key, value in metrics.items():
        if isinstance(value, float):
            if 'Return' in key or 'Drawdown' in key or 'Volatility' in key:
                print(f"{key:<30}: {value:>8.2f}%")
            elif 'Ratio' in key:
                print(f"{key:<30}: {value:>8.3f}")
            else:
                print(f"{key:<30}: ${value:>8,.2f}")
    
    print(f"\nAnzahl Trades: {len(trades)}")
    print("="*60)
    
    # Visualisierungen
    plot_results(df, metrics)
    
    print("\n🎉 Backtest abgeschlossen!")
    print("📊 Ergebnisse gespeichert als 'backtest_results.png'")

if __name__ == "__main__":
    main()
