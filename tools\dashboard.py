#!/usr/bin/env python3
"""
System Dashboard for V5_Trader - Real-time system monitoring
"""
import os
import sys
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.system_monitor import SystemMonitor


class V5TraderDashboard:
    """Real-time dashboard for V5_Trader system"""
    
    def __init__(self):
        self.monitor = SystemMonitor()
        self.start_time = datetime.now()
        self.update_count = 0
        
    def clear_screen(self):
        """Clear terminal screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def get_uptime(self) -> str:
        """Get system uptime"""
        uptime = datetime.now() - self.start_time
        hours, remainder = divmod(int(uptime.total_seconds()), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def get_system_info(self) -> Dict:
        """Get basic system information"""
        try:
            import psutil
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('.')
            
            return {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_used": memory.used // (1024**3),  # GB
                "memory_total": memory.total // (1024**3),  # GB
                "disk_percent": disk.percent,
                "disk_free": disk.free // (1024**3),  # GB
                "disk_total": disk.total // (1024**3)  # GB
            }
        except ImportError:
            return {
                "cpu_percent": "N/A",
                "memory_percent": "N/A",
                "memory_used": "N/A",
                "memory_total": "N/A",
                "disk_percent": "N/A",
                "disk_free": "N/A",
                "disk_total": "N/A"
            }
    
    def get_project_stats(self) -> Dict:
        """Get project-specific statistics"""
        stats = {
            "data_files": 0,
            "model_files": 0,
            "log_files": 0,
            "test_files": 0,
            "total_lines": 0
        }
        
        # Count files
        for root, dirs, files in os.walk('.'):
            # Skip virtual environment and hidden directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d != 'venv']
            
            for file in files:
                if file.endswith('.csv'):
                    stats["data_files"] += 1
                elif file.endswith('.pkl') or file.endswith('.pth'):
                    stats["model_files"] += 1
                elif file.endswith('.log'):
                    stats["log_files"] += 1
                elif file.startswith('test_') and file.endswith('.py'):
                    stats["test_files"] += 1
                
                # Count lines in Python files
                if file.endswith('.py'):
                    try:
                        with open(os.path.join(root, file), 'r', encoding='utf-8') as f:
                            stats["total_lines"] += len(f.readlines())
                    except:
                        pass
        
        return stats
    
    def format_service_status(self, results: List[Dict]) -> str:
        """Format service status for display"""
        output = []
        
        for result in results:
            service = result['service'].ljust(15)
            status = result['status']
            
            if 'response_time' in result and result['response_time']:
                rt = f"({result['response_time']:.3f}s)"
            else:
                rt = ""
            
            line = f"  {service}: {status} {rt}"
            output.append(line)
            
            if 'error' in result:
                output.append(f"    └─ Error: {result['error']}")
        
        return "\n".join(output)
    
    def render_dashboard(self):
        """Render the complete dashboard"""
        self.clear_screen()
        
        # Header
        print("╔" + "═" * 78 + "╗")
        print("║" + " V5_TRADER SYSTEM DASHBOARD ".center(78) + "║")
        print("╠" + "═" * 78 + "╣")
        
        # System Info
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        uptime = self.get_uptime()
        health_score = self.monitor.get_system_health_score()
        
        print(f"║ Time: {current_time}  │  Uptime: {uptime}  │  Health: {health_score:.1f}% ║")
        print("╠" + "═" * 78 + "╣")
        
        # System Resources
        sys_info = self.get_system_info()
        print("║ SYSTEM RESOURCES" + " " * 61 + "║")
        print(f"║   CPU: {str(sys_info['cpu_percent']).rjust(6)}%  │  " +
              f"Memory: {sys_info['memory_used']}/{sys_info['memory_total']} GB " +
              f"({str(sys_info['memory_percent']).rjust(5)}%)  │  " +
              f"Disk: {str(sys_info['disk_percent']).rjust(5)}% ║")
        print("╠" + "═" * 78 + "╣")
        
        # Service Status
        print("║ SERVICE STATUS" + " " * 64 + "║")
        results = self.monitor.generate_status_report()
        status_text = self.format_service_status(results)
        
        for line in status_text.split('\n'):
            print("║" + line.ljust(78) + "║")
        
        print("╠" + "═" * 78 + "╣")
        
        # Project Statistics
        stats = self.get_project_stats()
        print("║ PROJECT STATISTICS" + " " * 60 + "║")
        print(f"║   Data Files: {str(stats['data_files']).rjust(3)}  │  " +
              f"Model Files: {str(stats['model_files']).rjust(3)}  │  " +
              f"Test Files: {str(stats['test_files']).rjust(3)}  │  " +
              f"Total Lines: {str(stats['total_lines']).rjust(6)} ║")
        print("╠" + "═" * 78 + "╣")
        
        # Recent Activity (placeholder)
        print("║ RECENT ACTIVITY" + " " * 63 + "║")
        print("║   • System monitoring active" + " " * 47 + "║")
        print("║   • WebSocket server running" + " " * 47 + "║")
        print("║   • Model training available" + " " * 47 + "║")
        print("╠" + "═" * 78 + "╣")
        
        # Footer
        self.update_count += 1
        print(f"║ Updates: {str(self.update_count).rjust(4)}  │  " +
              "Press Ctrl+C to exit  │  " +
              "Refresh: 10s" + " " * 25 + "║")
        print("╚" + "═" * 78 + "╝")
    
    def run_dashboard(self, refresh_interval=10):
        """Run the dashboard with automatic refresh"""
        print("🚀 Starting V5_Trader Dashboard...")
        print("Press Ctrl+C to exit")
        time.sleep(2)
        
        try:
            while True:
                self.render_dashboard()
                time.sleep(refresh_interval)
        except KeyboardInterrupt:
            self.clear_screen()
            print("👋 Dashboard stopped!")
            print("\nFinal System Status:")
            self.monitor.print_status()


def main():
    """Main function"""
    dashboard = V5TraderDashboard()
    dashboard.run_dashboard()


if __name__ == "__main__":
    main()
