from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from typing import Dict, Any
import os
from dotenv import load_dotenv

load_dotenv()

app = FastAPI(
    title="V5 Trader API Gateway",
    description="AI Trading Platform API Gateway",
    version="1.0.0"
)

# CORS Middleware für Frontend-Zugriff
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "V5 Trader API Gateway", "status": "running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "api-gateway"}

@app.get("/api/market-data")
async def get_market_data(symbol: str = "AAPL"):
    """Get market data for a symbol"""
    try:
        # Hier würden wir später die Alpha Vantage API aufrufen
        return {
            "symbol": symbol,
            "price": 150.00,
            "change": "+2.50",
            "change_percent": "+1.69%",
            "timestamp": "2025-07-20T21:25:00"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
