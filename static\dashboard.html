<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V5 Trader Dashboard</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .header { 
            background: rgba(255, 255, 255, 0.95); 
            color: #2c3e50; 
            padding: 20px; 
            border-radius: 15px; 
            margin-bottom: 20px; 
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        .container { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); 
            gap: 20px; 
        }
        .card { 
            background: rgba(255, 255, 255, 0.95); 
            padding: 25px; 
            border-radius: 15px; 
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        .card h3 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .metric { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin: 15px 0; 
            padding: 10px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 8px;
        }
        .metric-label { 
            font-weight: 600; 
            color: #34495e;
        }
        .metric-value { 
            font-size: 1.2em; 
            font-weight: bold;
            color: #2c3e50; 
        }
        .status-up { color: #27ae60; }
        .status-down { color: #e74c3c; }
        .status-warning { color: #f39c12; }
        .price-list { 
            max-height: 350px; 
            overflow-y: auto; 
            border: 1px solid #ecf0f1;
            border-radius: 8px;
        }
        .price-item { 
            display: flex; 
            justify-content: space-between; 
            align-items: center;
            padding: 12px 15px; 
            border-bottom: 1px solid #ecf0f1;
            transition: background-color 0.2s ease;
        }
        .price-item:hover {
            background-color: rgba(52, 152, 219, 0.1);
        }
        .price-item:last-child {
            border-bottom: none;
        }
        .chart-placeholder { 
            height: 250px; 
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border: 2px dashed #dee2e6; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            color: #6c757d; 
            border-radius: 10px;
            font-size: 1.1em;
            text-align: center;
        }
        .connection-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
            animation: pulse 2s infinite;
        }
        .connected { background-color: #27ae60; }
        .disconnected { background-color: #e74c3c; }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .signal-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.9em;
            font-weight: bold;
            text-transform: uppercase;
        }
        .signal-buy { background-color: #d4edda; color: #155724; }
        .signal-sell { background-color: #f8d7da; color: #721c24; }
        .signal-hold { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 V5 Trader Dashboard</h1>
        <p>AI-Powered Trading Platform | Status: 
            <span id="connection-status">Verbinde...</span>
            <span id="connection-indicator" class="connection-indicator disconnected"></span>
        </p>
        <p><small>Letzte Aktualisierung: <span id="last-update">--</span></small></p>
    </div>
    
    <div class="container">
        <!-- System Status -->
        <div class="card">
            <h3>🔧 System Status</h3>
            <div class="metric">
                <span class="metric-label">API Gateway:</span>
                <span class="metric-value" id="api-status"><div class="loading"></div></span>
            </div>
            <div class="metric">
                <span class="metric-label">MLflow:</span>
                <span class="metric-value" id="mlflow-status"><div class="loading"></div></span>
            </div>
            <div class="metric">
                <span class="metric-label">WebSocket:</span>
                <span class="metric-value" id="websocket-status"><div class="loading"></div></span>
            </div>
            <div class="metric">
                <span class="metric-label">System Health:</span>
                <span class="metric-value" id="system-health">--</span>
            </div>
        </div>
        
        <!-- Portfolio Overview -->
        <div class="card">
            <h3>💼 Portfolio Übersicht</h3>
            <div class="metric">
                <span class="metric-label">Gesamtwert:</span>
                <span class="metric-value" id="portfolio-value">$100,000.00</span>
            </div>
            <div class="metric">
                <span class="metric-label">Tagesgewinn:</span>
                <span class="metric-value status-up" id="daily-pnl">+$1,234.56 (+1.23%)</span>
            </div>
            <div class="metric">
                <span class="metric-label">Aktive Positionen:</span>
                <span class="metric-value" id="active-positions">5</span>
            </div>
            <div class="metric">
                <span class="metric-label">Verfügbar:</span>
                <span class="metric-value" id="available-cash">$45,678.90</span>
            </div>
        </div>
        
        <!-- Live Prices -->
        <div class="card">
            <h3>📈 Live Kurse</h3>
            <div class="price-list" id="price-list">
                <div class="price-item">
                    <span>Warte auf Live-Daten...</span>
                    <span><div class="loading"></div></span>
                </div>
            </div>
        </div>
        
        <!-- Trading Signals -->
        <div class="card">
            <h3>🤖 AI Trading Signale</h3>
            <div class="metric" id="signals-container">
                <div class="metric">
                    <span class="metric-label">AAPL:</span>
                    <span class="metric-value">
                        <span class="signal-badge signal-buy">BUY</span>
                        <small>(85% Konfidenz)</small>
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">MSFT:</span>
                    <span class="metric-value">
                        <span class="signal-badge signal-sell">SELL</span>
                        <small>(72% Konfidenz)</small>
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">GOOGL:</span>
                    <span class="metric-value">
                        <span class="signal-badge signal-hold">HOLD</span>
                        <small>(45% Konfidenz)</small>
                    </span>
                </div>
            </div>
        </div>
        
        <!-- Performance Chart -->
        <div class="card">
            <h3>📊 Performance Chart</h3>
            <div class="chart-placeholder">
                📈 Chart wird hier angezeigt<br>
                <small>(Implementierung mit Chart.js)</small><br><br>
                <button onclick="loadChart()" style="padding: 10px 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    Chart laden
                </button>
            </div>
        </div>
        
        <!-- Recent Trades -->
        <div class="card">
            <h3>📋 Letzte Trades</h3>
            <div class="price-list" id="trades-list">
                <div class="price-item">
                    <span><strong>AAPL</strong> BUY 100</span>
                    <span class="status-up">$150.25</span>
                </div>
                <div class="price-item">
                    <span><strong>MSFT</strong> SELL 50</span>
                    <span class="status-down">$305.80</span>
                </div>
                <div class="price-item">
                    <span><strong>TSLA</strong> BUY 25</span>
                    <span class="status-up">$825.40</span>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // WebSocket Verbindung für Live-Daten
        let socket;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;
        
        function connectWebSocket() {
            try {
                socket = new WebSocket('ws://localhost:8765');
                
                socket.onopen = function(event) {
                    console.log('WebSocket verbunden');
                    document.getElementById('connection-status').textContent = 'Verbunden ✅';
                    document.getElementById('connection-indicator').className = 'connection-indicator connected';
                    document.getElementById('websocket-status').innerHTML = '<span class="status-up">✅ Online</span>';
                    reconnectAttempts = 0;
                    
                    // Subscribe to market data
                    socket.send(JSON.stringify({
                        type: "subscribe",
                        symbols: ["AAPL", "MSFT", "GOOGL", "TSLA", "AMZN"]
                    }));
                };
                
                socket.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                    updateLastUpdate();
                };
                
                socket.onclose = function(event) {
                    console.log('WebSocket getrennt');
                    document.getElementById('connection-status').textContent = 'Getrennt ❌';
                    document.getElementById('connection-indicator').className = 'connection-indicator disconnected';
                    document.getElementById('websocket-status').innerHTML = '<span class="status-down">❌ Offline</span>';
                    
                    // Versuche Wiederverbindung
                    if (reconnectAttempts < maxReconnectAttempts) {
                        reconnectAttempts++;
                        console.log(`Wiederverbindung Versuch ${reconnectAttempts}/${maxReconnectAttempts}`);
                        setTimeout(connectWebSocket, 5000);
                    }
                };
                
                socket.onerror = function(error) {
                    console.error('WebSocket Fehler:', error);
                };
                
            } catch (error) {
                console.error('WebSocket Verbindungsfehler:', error);
                document.getElementById('websocket-status').innerHTML = '<span class="status-down">❌ Fehler</span>';
            }
        }
        
        function handleWebSocketMessage(data) {
            switch(data.type) {
                case 'market_update':
                    updatePriceList(data);
                    break;
                case 'welcome':
                    console.log('Welcome message:', data.message);
                    break;
                case 'subscription_confirmed':
                    console.log('Subscription confirmed:', data.symbols);
                    break;
                case 'status':
                    updateSystemStatus(data);
                    break;
            }
        }
        
        function updatePriceList(data) {
            const priceList = document.getElementById('price-list');
            let existingItem = document.getElementById(`price-${data.symbol}`);
            
            const changeClass = data.change >= 0 ? 'status-up' : 'status-down';
            const changeSymbol = data.change >= 0 ? '+' : '';
            const arrow = data.change >= 0 ? '↗' : '↘';
            
            const itemHtml = `
                <div class="price-item">
                    <span><strong>${data.symbol}</strong> ${arrow}</span>
                    <span>
                        <span class="metric-value">$${data.price.toFixed(2)}</span>
                        <span class="${changeClass}">${changeSymbol}${data.change.toFixed(2)}%</span>
                    </span>
                </div>
            `;
            
            if (existingItem) {
                existingItem.outerHTML = itemHtml;
            } else {
                // Remove loading message if it exists
                const loadingMsg = priceList.querySelector('.price-item span:contains("Warte auf")');
                if (loadingMsg) {
                    priceList.innerHTML = '';
                }
                
                const newItem = document.createElement('div');
                newItem.id = `price-${data.symbol}`;
                newItem.innerHTML = itemHtml;
                priceList.appendChild(newItem);
            }
        }
        
        function updateSystemStatus(data) {
            document.getElementById('system-health').textContent = `${data.connected_clients} Clients`;
        }
        
        function updateLastUpdate() {
            const now = new Date();
            document.getElementById('last-update').textContent = now.toLocaleTimeString();
        }
        
        // System Status prüfen
        async function checkSystemStatus() {
            const services = [
                { id: 'api-status', url: 'http://localhost:8000/health', name: 'API Gateway' },
                { id: 'mlflow-status', url: 'http://localhost:5000', name: 'MLflow' }
            ];
            
            for (const service of services) {
                try {
                    const response = await fetch(service.url, { 
                        method: 'GET',
                        mode: 'no-cors',
                        timeout: 5000 
                    });
                    document.getElementById(service.id).innerHTML = '<span class="status-up">✅ Online</span>';
                } catch (error) {
                    document.getElementById(service.id).innerHTML = '<span class="status-down">❌ Offline</span>';
                }
            }
        }
        
        function loadChart() {
            const chartDiv = document.querySelector('.chart-placeholder');
            chartDiv.innerHTML = `
                <div style="text-align: center;">
                    <div class="loading" style="margin: 20px auto;"></div>
                    <p>Chart wird geladen...</p>
                    <p><small>Für vollständige Chart-Funktionalität Chart.js einbinden</small></p>
                </div>
            `;
            
            // Simulate chart loading
            setTimeout(() => {
                chartDiv.innerHTML = `
                    <div style="text-align: center; color: #27ae60;">
                        <h3>📈 Portfolio Performance</h3>
                        <p style="font-size: 2em; margin: 20px 0;">+12.5%</p>
                        <p>Letzte 30 Tage</p>
                        <small>Demo-Chart - Echte Implementierung mit Chart.js</small>
                    </div>
                `;
            }, 2000);
        }
        
        // Initialisierung
        document.addEventListener('DOMContentLoaded', function() {
            console.log('V5 Trader Dashboard geladen');
            
            // WebSocket verbinden
            connectWebSocket();
            
            // System Status prüfen
            checkSystemStatus();
            
            // Status alle 30 Sekunden prüfen
            setInterval(checkSystemStatus, 30000);
            
            // WebSocket Status alle 10 Sekunden prüfen
            setInterval(() => {
                if (socket && socket.readyState === WebSocket.OPEN) {
                    socket.send(JSON.stringify({ type: "get_status" }));
                }
            }, 10000);
            
            updateLastUpdate();
        });
        
        // Cleanup beim Schließen
        window.addEventListener('beforeunload', function() {
            if (socket) {
                socket.close();
            }
        });
    </script>
</body>
</html>
