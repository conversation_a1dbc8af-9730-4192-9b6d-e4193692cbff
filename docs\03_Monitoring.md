# 🚀 V5_Trader MLflow, Prometheus & Grafana Integration

## Überblick

Das V5_Trader-System implementiert eine vollständige Enterprise-Monitoring-Lösung mit MLflow für ML-Operations, Prometheus für Metriken-Sammlung und Grafana für Visualisierung. Diese Integration bietet professionelle Überwachung und Management für das AI-Trading-System.

## 🏗️ Architektur-Übersicht

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │───▶│   Prometheus    │───▶│    Grafana      │
│  (FastAPI +     │    │  (Metrics DB)   │    │ (Visualization) │
│   Metrics)      │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     MLflow      │    │   Exporters     │    │   Dashboards    │
│ (ML Lifecycle)  │    │ (Postgres/Redis)│    │   & Alerts      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🧪 MLflow Integration

### **Experiment Tracking**
- **Automatisches Logging**: Hyperparameter, Metriken (Loss, MSE, MAE) während Training
- **Run Vergleiche**: Web-UI unter http://localhost:5000 mit Filter- und Chart-Funktionalität
- **Artifact Storage**: Modell-Checkpoints und Training-Artifacts

### **Modell-Register**
- **Versionierung**: Automatische Registrierung als `V5Trader_AdvancedTransformer`
- **Stage-Management**: Staging → Production Übergänge
- **Deployment-Ready**: ONNX/PyTorch Export für Produktionseinbindung

### **API Integration**
```python
# MLflow Tracking in API Gateway
with mlflow.start_run():
    mlflow.log_param("symbol", request.symbol)
    mlflow.log_param("model_version", model_version)
    mlflow.log_metric("prediction", prediction)
    mlflow.log_metric("confidence", confidence)
```

## 📊 Prometheus Integration

### **Metriken-Sammlung**
- **HTTP Requests**: `http_requests_total{method, endpoint, status}`
- **Response Times**: `http_request_duration_seconds` (Histogram)
- **Active Connections**: `active_connections`
- **Model Predictions**: `model_predictions_total{model_name, status}`
- **Cache Performance**: `cache_hits_total{cache_type}`

### **Scraping Konfiguration**
```yaml
scrape_configs:
  - job_name: 'v5trader-api-gateway'
    static_configs:
      - targets: ['api-gateway:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'v5trader-postgres'
    static_configs:
      - targets: ['postgres_exporter:9187']
```

### **Alert Rules**
- **High Latency**: P95 > 500ms für 2 Minuten
- **Error Rate**: >10% 5xx Responses für 5 Minuten
- **Service Down**: Service nicht erreichbar für 1 Minute
- **Resource Usage**: CPU >80%, Memory >90%, Disk >85%

## 📈 Grafana Integration

### **Dashboard Panels**
1. **API Gateway Metrics**
   - Request Rate (Requests/sec)
   - Response Time (95th percentile)
   - Active Connections
   - HTTP Status Codes

2. **Database Monitoring**
   - PostgreSQL Connections
   - Query Performance
   - Database Size

3. **System Resources**
   - CPU Usage
   - Memory Usage
   - Disk Space
   - Network I/O

4. **ML Model Performance**
   - Prediction Rate
   - Model Confidence
   - Error Rate

### **Datasource Konfiguration**
```yaml
datasources:
  - name: Prometheus
    type: prometheus
    url: http://prometheus:9090
    isDefault: true
```

## 🌐 API Gateway Integration

### **Prometheus Middleware**
```python
@app.middleware("http")
async def prometheus_middleware(request, call_next):
    start_time = time.time()
    ACTIVE_CONNECTIONS.inc()
    
    response = await call_next(request)
    
    duration = time.time() - start_time
    REQUEST_DURATION.labels(
        method=request.method,
        endpoint=request.url.path
    ).observe(duration)
    
    REQUEST_COUNT.labels(
        method=request.method,
        endpoint=request.url.path,
        status=response.status_code
    ).inc()
    
    ACTIVE_CONNECTIONS.dec()
    return response
```

### **Health Check Endpoint**
```python
@app.get("/health")
async def health_check():
    services = {
        "redis": check_redis_health(),
        "postgres": check_postgres_health(),
        "mlflow": check_mlflow_health()
    }
    
    return {
        "status": "healthy" if all(services.values()) else "degraded",
        "services": services,
        "metrics": collect_system_metrics()
    }
```

## 🐳 Docker Compose Setup

### **Service Definitionen**
```yaml
services:
  # MLflow Tracking Server
  mlflow:
    image: python:3.11-slim
    command: >
      bash -c "
        pip install mlflow psycopg2-binary prometheus_client &&
        mlflow server 
          --backend-store-uri ********************************************/v5trader
          --default-artifact-root /mlflow/artifacts
          --host 0.0.0.0 --port 5000
      "

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    ports:
      - "9090:9090"

  # Grafana Visualization
  grafana:
    image: grafana/grafana:latest
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=v5trader_grafana_2024
    volumes:
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    ports:
      - "3000:3000"
```

## 🚀 Deployment & Startup

### **Automatischer Start**
```bash
# Komplettes System mit Monitoring
python start_v5trader.py

# Nur Docker Services
docker-compose up -d

# Einzelne Services
docker-compose up -d postgres redis mlflow
docker-compose up -d prometheus grafana
```

### **Service URLs**
- **MLflow UI**: http://localhost:5000
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/v5trader_grafana_2024)
- **API Gateway**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Metrics**: http://localhost:8000/metrics

## 🧪 Testing & Validation

### **Monitoring Integration Test**
```bash
# Vollständiger Monitoring-Test
python test_monitoring_integration.py

# Einzelne Komponenten testen
curl http://localhost:5000/api/2.0/mlflow/experiments/list
curl http://localhost:9090/api/v1/targets
curl http://localhost:3000/api/health
curl http://localhost:8000/metrics
```

### **Test Coverage**
- ✅ Docker Services Status
- ✅ MLflow API & UI
- ✅ Prometheus Targets & Metrics
- ✅ Grafana Health & UI
- ✅ API Gateway Metrics Endpoint
- ✅ End-to-End Metrics Flow

## 📊 Key Performance Indicators

### **System Metrics**
- **Request Latency**: P95 < 100ms
- **Error Rate**: < 1%
- **Uptime**: > 99.9%
- **Resource Usage**: CPU < 80%, Memory < 90%

### **ML Metrics**
- **Prediction Rate**: Tracked per model
- **Model Confidence**: Average > 70%
- **Training Metrics**: Loss, MSE, MAE progression
- **Model Deployment**: Version tracking and rollback

## 🔧 Konfiguration & Anpassung

### **Environment Variables**
```bash
# .env Datei
POSTGRES_PASSWORD=v5trader_secure_2024
REDIS_PASSWORD=v5trader_redis_2024
GRAFANA_PASSWORD=v5trader_grafana_2024
MLFLOW_TRACKING_URI=http://mlflow:5000
```

### **Custom Metrics**
```python
# Neue Metriken hinzufügen
CUSTOM_METRIC = Counter('custom_metric_total', 'Description', ['label1', 'label2'])

# In API Endpoint verwenden
CUSTOM_METRIC.labels(label1="value1", label2="value2").inc()
```

## 🚨 Troubleshooting

### **Häufige Probleme**
1. **MLflow nicht erreichbar**: PostgreSQL Connection prüfen
2. **Prometheus keine Targets**: Service Discovery konfigurieren
3. **Grafana keine Daten**: Datasource Verbindung prüfen
4. **API Metrics fehlen**: Prometheus Middleware aktiviert?

### **Debug Commands**
```bash
# Service Status prüfen
docker-compose ps
docker-compose logs mlflow
docker-compose logs prometheus

# Metrics manuell abrufen
curl http://localhost:8000/metrics
curl http://localhost:9090/api/v1/targets
```

## 🎯 Production Best Practices

### **Security**
- Authentifizierung für Grafana aktivieren
- API Gateway Rate Limiting
- Prometheus Access Control
- MLflow Artifact Encryption

### **Performance**
- Prometheus Retention konfigurieren
- Grafana Query Optimization
- MLflow Artifact Storage (S3/MinIO)
- Database Connection Pooling

### **Monitoring**
- Alert Rules für kritische Metriken
- Backup Strategien für Metrics
- Log Aggregation (ELK Stack)
- Distributed Tracing (Jaeger)

---

**V5_Trader Monitoring Stack** - Enterprise-grade Observability für AI Trading Systems 🚀📊💰
