# V5_Trader Model Configuration
# Centralized configuration for hyperparameters and model settings

model:
  name: "V5Trader_AdvancedTransformer"
  version: "1.0.0"
  
  # Architecture parameters
  architecture:
    input_features: 5
    sequence_length: 120
    d_model: 512
    n_heads: 8
    n_encoder_layers: 6
    dropout: 0.1
    cnn_filters: 32
    lstm_units: 64
    output_size: 1
    
  # Training parameters
  training:
    learning_rate: 1e-4
    weight_decay: 1e-5
    batch_size: 32
    max_epochs: 100
    patience: 10
    min_delta: 1e-4
    
  # Advanced features
  features:
    use_mixed_precision: true
    uncertainty_estimation: true
    ensemble_size: 5
    gradient_clip_val: 1.0
    
  # Data parameters
  data:
    val_split: 0.1
    test_split: 0.1
    sequence_overlap: 0.5
    normalize: true
    
# MLflow configuration
mlflow:
  tracking_uri: "http://localhost:5000"
  experiment_name: "V5_Trader_Experiments"
  run_name_prefix: "transformer_run"
  
  # Logging settings
  logging:
    log_params: true
    log_metrics: true
    log_artifacts: true
    log_model: true
    
# Training environment
environment:
  device: "auto"  # auto, cpu, cuda
  num_workers: 4
  pin_memory: true
  persistent_workers: true
  
# Paths
paths:
  data_dir: "data"
  model_dir: "models"
  log_dir: "logs"
  checkpoint_dir: "checkpoints"
