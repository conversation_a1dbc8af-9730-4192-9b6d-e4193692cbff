# data/fin_data_module.py
import pandas as pd
import torch
from torch.utils.data import DataLoader, Dataset
import pytorch_lightning as pl

class TimeSeriesDataset(Dataset):
    def __init__(self, features: pd.DataFrame, targets: pd.Series, seq_len: int):
        # 1) Nur numerische Spalten verwenden und NaNs füllen
        features_num = features.select_dtypes(include=["number"]).fillna(method="ffill").fillna(0.0)
        targets_num  = targets.fillna(method="ffill").fillna(0.0)

        self.features = torch.tensor(features_num.values, dtype=torch.float32)
        self.targets  = torch.tensor(targets_num.values, dtype=torch.float32)
        self.seq_len  = seq_len

    def __len__(self):
        return len(self.features) - self.seq_len

    def __getitem__(self, idx):
        x = self.features[idx : idx + self.seq_len]
        y = self.targets[idx + self.seq_len]
        return x, y

class FinancialDataModule(pl.LightningDataModule):
    def __init__(self, features: pd.DataFrame, targets: pd.Series,
                 seq_len: int = 120, batch_size: int = 64, val_split: float = 0.2):
        super().__init__()
        self.features = features
        self.targets  = targets
        self.seq_len  = seq_len
        self.batch_size = batch_size
        self.val_split  = val_split

    def setup(self, stage=None):
        dataset = TimeSeriesDataset(self.features, self.targets, self.seq_len)
        val_size = int(len(dataset) * self.val_split)
        train_size = len(dataset) - val_size
        self.train_dataset, self.val_dataset = torch.utils.data.random_split(
            dataset, [train_size, val_size]
        )

    def train_dataloader(self):
        return DataLoader(self.train_dataset, batch_size=self.batch_size, shuffle=True)

    def val_dataloader(self):
        return DataLoader(self.val_dataset, batch_size=self.batch_size)
