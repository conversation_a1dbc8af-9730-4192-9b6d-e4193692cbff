# 🚀 V5_Trader - Quick Start Guide

## ✅ **System ist jetzt bereit!**

Alle Docker-Services laufen und das System ist einsatzbereit.

## 🌐 **Verfügbare Dashboards**

| Service | URL | Beschreibung | Login |
|---------|-----|--------------|-------|
| 📊 **MLflow** | http://localhost:5000 | ML-Experiment Tracking | - |
| 📈 **Grafana** | http://localhost:3000 | Monitoring Dashboard | admin/admin |
| 🔍 **Prometheus** | http://localhost:9091 | Metrics Collection | - |
| 🚀 **API Docs** | http://localhost:8000/docs | API Documentation | - |

## 🧠 **1. Model Training starten**

```bash
# Transformer-Modell trainieren
python train_model.py
```

**Was passiert:**
- Lädt Marktdaten aus `data/market_data.csv`
- Berechnet technische Indikatoren
- Trainiert ein Transformer-Modell
- Speichert Ergebnisse in MLflow
- Erstellt Model-Checkpoints

**Ergebnisse:**
- Trainierte Modelle in `models/checkpoints/`
- Experiment-Tracking in MLflow UI
- Performance-Metriken und Logs

## 📈 **2. Backtest durchführen**

```bash
# Einfachen Backtest ausführen
python run_backtest.py
```

**Was passiert:**
- Implementiert Momentum-Strategie
- Simuliert Trading über historische Daten
- Berechnet Performance-Metriken
- Erstellt Visualisierungen

**Ergebnisse:**
- Performance-Report in der Konsole
- Grafiken als `backtest_results.png`
- Vergleich mit Buy & Hold

## 🔧 **3. Services verwalten**

### Services starten:
```bash
# Alle Services starten
python start_v5trader.py

# Oder einzeln mit Docker Compose
docker-compose up -d postgres redis mlflow prometheus grafana
```

### Services stoppen:
```bash
# Alle Services stoppen
docker-compose down

# Oder mit Volumes löschen
docker-compose down -v
```

### Service-Status prüfen:
```bash
# Status aller Services
docker-compose ps

# Logs anzeigen
docker-compose logs mlflow
docker-compose logs postgres
```

## 📊 **4. Daten verwalten**

### Neue Marktdaten hinzufügen:
```python
import yfinance as yf
import pandas as pd

# Neue Daten laden
data = yf.download("AAPL", start="2020-01-01", end="2024-01-01")
data.reset_index().to_csv("data/market_data.csv", index=False)
```

### Datenqualität prüfen:
```bash
# Daten-Info anzeigen
python -c "
import pandas as pd
df = pd.read_csv('data/market_data.csv')
print(f'Datenpunkte: {len(df)}')
print(f'Zeitraum: {df[\"Date\"].min()} bis {df[\"Date\"].max()}')
print(f'Spalten: {list(df.columns)}')
"
```

## 🎯 **5. Custom Strategien entwickeln**

### Neue Strategie erstellen:
```python
# strategies/my_strategy.py
from strategies.base_strategy import BaseStrategy

class MyCustomStrategy(BaseStrategy):
    def __init__(self, **kwargs):
        super().__init__("MyStrategy", kwargs)
        
    def generate_signals(self, data):
        # Ihre Trading-Logik hier
        signals = {}
        # ... Implementation
        return signals
```

### Strategie testen:
```python
# Im Backtest verwenden
from strategies.my_strategy import MyCustomStrategy

strategy = MyCustomStrategy(param1=value1)
# ... Backtest-Code
```

## 🔍 **6. Monitoring und Debugging**

### MLflow verwenden:
1. Öffne http://localhost:5000
2. Wähle Experiment "V5Trader_Training"
3. Vergleiche verschiedene Runs
4. Lade Modelle herunter

### Grafana Dashboard:
1. Öffne http://localhost:3000
2. Login: admin/admin
3. Importiere Dashboard-Templates
4. Überwache System-Metriken

### Logs analysieren:
```bash
# Container-Logs
docker-compose logs -f mlflow
docker-compose logs -f postgres

# Python-Logs
tail -f logs/v5trader.log
```

## 🛠️ **7. Erweiterte Konfiguration**

### Environment-Variablen anpassen:
```bash
# .env Datei bearbeiten
nano .env

# Wichtige Einstellungen:
POSTGRES_PASSWORD=your_secure_password
TRADING_MODE=paper  # oder live
MAX_POSITION_SIZE=0.1
```

### Model-Parameter anpassen:
```python
# In train_model.py
model = SimpleTransformer(
    d_model=256,        # Größeres Modell
    n_heads=16,         # Mehr Attention-Heads
    n_layers=8,         # Tieferes Netzwerk
    learning_rate=5e-5  # Kleinere Lernrate
)
```

## 🚨 **8. Troubleshooting**

### Häufige Probleme:

**Docker-Services starten nicht:**
```bash
# Ports prüfen
netstat -tulpn | grep :5432
netstat -tulpn | grep :6379

# Services neu starten
docker-compose down && docker-compose up -d
```

**MLflow nicht erreichbar:**
```bash
# Container-Status prüfen
docker-compose ps mlflow

# Logs prüfen
docker-compose logs mlflow
```

**Training schlägt fehl:**
```bash
# Dependencies prüfen
pip install -r requirements.txt

# CUDA verfügbar?
python -c "import torch; print(torch.cuda.is_available())"
```

**Daten nicht gefunden:**
```bash
# Daten-Pfad prüfen
ls -la data/market_data.csv

# Daten neu laden
python -c "
import yfinance as yf
data = yf.download('AAPL', start='2020-01-01')
data.reset_index().to_csv('data/market_data.csv')
"
```

## 🎯 **9. Nächste Schritte**

### Für Anfänger:
1. ✅ Führe ersten Backtest aus: `python run_backtest.py`
2. ✅ Trainiere erstes Modell: `python train_model.py`
3. ✅ Erkunde MLflow Dashboard
4. ✅ Experimentiere mit Parametern

### Für Fortgeschrittene:
1. 🔧 Entwickle eigene Trading-Strategien
2. 🔧 Implementiere neue Features
3. 🔧 Optimiere Model-Architektur
4. 🔧 Integriere Live-Daten

### Für Experten:
1. ⚙️ Setup Production-Environment
2. ⚙️ Implementiere Risk-Management
3. ⚙️ Entwickle Multi-Asset-Strategien
4. ⚙️ Integriere Alternative Datenquellen

## 📞 **Support**

- 📚 Dokumentation: Siehe README.md
- 🐛 Issues: GitHub Issues
- 💬 Diskussionen: GitHub Discussions
- 📧 Email: <EMAIL>

---

## 🎉 **Viel Erfolg mit V5_Trader!**

Das System ist jetzt vollständig eingerichtet und bereit für den Einsatz. Beginnen Sie mit einem einfachen Backtest und arbeiten Sie sich zu komplexeren Strategien vor.

**Happy Trading! 🚀📈**
