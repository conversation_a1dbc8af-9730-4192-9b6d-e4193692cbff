# V5_Trader Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=v5trader
POSTGRES_USER=postgres
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_PASSWORD=your_redis_password_here
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# =============================================================================
# MLFLOW CONFIGURATION
# =============================================================================
MLFLOW_TRACKING_URI=http://localhost:5000
MLFLOW_EXPERIMENT_NAME=V5Trader_Development
MLFLOW_ARTIFACT_ROOT=./mlflow/artifacts
MLFLOW_BACKEND_STORE_URI=sqlite:///mlflow.db

# =============================================================================
# API KEYS (Optional - for enhanced data sources)
# =============================================================================
# Alpha Vantage API Key (for premium market data)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here

# Polygon.io API Key (for real-time data)
POLYGON_API_KEY=your_polygon_key_here

# IEX Cloud API Key (alternative data source)
IEX_CLOUD_API_KEY=your_iex_cloud_key_here

# =============================================================================
# MONITORING AND ALERTING
# =============================================================================
# Grafana Admin Password
GRAFANA_ADMIN_PASSWORD=admin_password_here

# Slack Webhook for Alerts (optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Email Configuration for Alerts (optional)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password_here

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Secret for API Authentication
JWT_SECRET_KEY=your_jwt_secret_key_here_make_it_long_and_random

# API Rate Limiting
RATE_LIMIT_PER_MINUTE=100

# CORS Origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://localhost:8000

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================
# Trading Mode (paper/live)
TRADING_MODE=paper

# Broker Configuration (if using live trading)
BROKER_API_KEY=your_broker_api_key_here
BROKER_SECRET_KEY=your_broker_secret_key_here
BROKER_BASE_URL=https://paper-api.alpaca.markets  # Paper trading URL

# Risk Management
MAX_POSITION_SIZE=0.1  # Maximum 10% of portfolio per position
MAX_DAILY_LOSS=0.05    # Maximum 5% daily loss
STOP_LOSS_PERCENT=0.02 # 2% stop loss

# =============================================================================
# MODEL CONFIGURATION
# =============================================================================
# Model Training
MODEL_BATCH_SIZE=32
MODEL_LEARNING_RATE=0.0001
MODEL_MAX_EPOCHS=100

# Model Inference
MODEL_INFERENCE_BATCH_SIZE=64
MODEL_UNCERTAINTY_SAMPLES=100

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE_MAX_SIZE=10MB
LOG_FILE_BACKUP_COUNT=5

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Debug Mode
DEBUG=false

# Development Database (SQLite for local development)
DEV_DATABASE_URL=sqlite:///v5trader_dev.db

# Test Configuration
TEST_DATABASE_URL=sqlite:///v5trader_test.db
TEST_DATA_SIZE=1000  # Number of test data points

# =============================================================================
# CLOUD CONFIGURATION (for production deployment)
# =============================================================================
# AWS Configuration (if using AWS)
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1
AWS_S3_BUCKET=v5trader-artifacts

# Google Cloud Configuration (if using GCP)
GOOGLE_CLOUD_PROJECT=your_gcp_project_id
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# Azure Configuration (if using Azure)
AZURE_STORAGE_CONNECTION_STRING=your_azure_connection_string

# =============================================================================
# KUBERNETES CONFIGURATION
# =============================================================================
# Kubernetes Namespace
K8S_NAMESPACE=v5trader

# Resource Limits
K8S_CPU_LIMIT=2
K8S_MEMORY_LIMIT=4Gi
K8S_CPU_REQUEST=1
K8S_MEMORY_REQUEST=2Gi

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# Backup Schedule (cron format)
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM

# Backup Retention (days)
BACKUP_RETENTION_DAYS=30

# Backup Storage Location
BACKUP_STORAGE_PATH=/backups/v5trader

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/Disable Features
ENABLE_LIVE_TRADING=false
ENABLE_PAPER_TRADING=true
ENABLE_BACKTESTING=true
ENABLE_MODEL_TRAINING=true
ENABLE_REAL_TIME_INFERENCE=true
ENABLE_MONITORING=true
ENABLE_ALERTS=true

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
# Worker Processes
API_WORKERS=4
WEBSOCKET_WORKERS=2

# Connection Pools
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
REDIS_POOL_SIZE=10

# Cache Configuration
CACHE_TTL=300  # 5 minutes
CACHE_MAX_SIZE=1000

# =============================================================================
# NOTES
# =============================================================================
# 1. Never commit this file with real secrets to version control
# 2. Use strong, unique passwords for all services
# 3. Rotate secrets regularly in production
# 4. Consider using a secrets management service (AWS Secrets Manager, HashiCorp Vault, etc.)
# 5. For Kubernetes deployment, create secrets using kubectl create secret
# 6. Validate all environment variables on application startup
